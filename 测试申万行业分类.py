import tushare as ts
import pandas as pd
import time

# 初始化tushare pro
TOKEN = '2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211'
pro = ts.pro_api(TOKEN)

def test_industry_api_old(ts_code):
    """测试旧版申万行业分类API"""
    print(f"=== 测试旧版API: stock_industry ===")
    try:
        stock_industry = pro.stock_industry(
            ts_code=ts_code,
            fields='ts_code,industry_name,industry_code,level'
        )
        
        print(f"返回数据条数: {len(stock_industry)}")
        if not stock_industry.empty:
            print("数据内容:")
            for _, row in stock_industry.iterrows():
                print(f"  {row['level']}: {row['industry_name']} ({row['industry_code']})")
        else:
            print("未获取到数据")
        
        return stock_industry
    except Exception as e:
        print(f"旧版API失败: {e}")
        return None

def test_industry_api_new(ts_code):
    """测试新版申万行业分类API"""
    print(f"=== 测试新版API: index_member_all ===")
    try:
        industry_data = pro.index_member_all(
            ts_code=ts_code,
            is_new='Y'  # 获取最新分类
        )
        
        print(f"返回数据条数: {len(industry_data)}")
        if not industry_data.empty:
            print("数据内容:")
            for _, row in industry_data.iterrows():
                print(f"  一级: {row.get('l1_name', '')} ({row.get('l1_code', '')})")
                print(f"  二级: {row.get('l2_name', '')} ({row.get('l2_code', '')})")
                print(f"  三级: {row.get('l3_name', '')} ({row.get('l3_code', '')})")
                print(f"  纳入日期: {row.get('in_date', '')}")
                print(f"  是否最新: {row.get('is_new', '')}")
                print("  ---")
        else:
            print("未获取到数据")
        
        return industry_data
    except Exception as e:
        print(f"新版API失败: {e}")
        return None

def get_industry_info_improved(ts_code):
    """改进的申万行业分类获取函数"""
    print(f"=== 获取 {ts_code} 的申万行业分类 ===")
    
    industry_info = {
        '申万一级行业': '',
        '申万二级行业': '',
        '申万三级行业': ''
    }
    
    # 首先尝试新版API
    try:
        print("尝试新版API...")
        industry_data = pro.index_member_all(
            ts_code=ts_code,
            is_new='Y'
        )
        
        if not industry_data.empty:
            # 取第一条记录（最新的分类）
            row = industry_data.iloc[0]
            industry_info = {
                '申万一级行业': row.get('l1_name', ''),
                '申万二级行业': row.get('l2_name', ''),
                '申万三级行业': row.get('l3_name', '')
            }
            print(f"✓ 新版API成功")
            print(f"  一级行业: {industry_info['申万一级行业']}")
            print(f"  二级行业: {industry_info['申万二级行业']}")
            print(f"  三级行业: {industry_info['申万三级行业']}")
            return industry_info
        else:
            print("✗ 新版API返回空数据")
    except Exception as e:
        print(f"✗ 新版API失败: {e}")
    
    # 如果新版API失败，尝试旧版API
    try:
        print("尝试旧版API...")
        stock_industry = pro.stock_industry(
            ts_code=ts_code,
            fields='ts_code,industry_name,industry_code,level'
        )
        
        if not stock_industry.empty:
            for _, row in stock_industry.iterrows():
                if row['level'] == 'L1':
                    industry_info['申万一级行业'] = row['industry_name']
                elif row['level'] == 'L2':
                    industry_info['申万二级行业'] = row['industry_name']
                elif row['level'] == 'L3':
                    industry_info['申万三级行业'] = row['industry_name']
            
            print(f"✓ 旧版API成功")
            print(f"  一级行业: {industry_info['申万一级行业']}")
            print(f"  二级行业: {industry_info['申万二级行业']}")
            print(f"  三级行业: {industry_info['申万三级行业']}")
            return industry_info
        else:
            print("✗ 旧版API返回空数据")
    except Exception as e:
        print(f"✗ 旧版API失败: {e}")
    
    print("✗ 所有API都失败")
    return industry_info

def test_multiple_stocks():
    """测试多只股票的行业分类"""
    test_stocks = [
        '000001.SZ',  # 平安银行
        '000002.SZ',  # 万科A
        '600000.SH',  # 浦发银行
        '600036.SH',  # 招商银行
        '000858.SZ',  # 五粮液
    ]
    
    print("=" * 60)
    print("测试多只股票的申万行业分类")
    print("=" * 60)
    
    results = []
    
    for i, ts_code in enumerate(test_stocks):
        print(f"\n[{i+1}/{len(test_stocks)}] 测试股票: {ts_code}")
        
        # 获取股票名称
        try:
            stock_info = pro.stock_basic(ts_code=ts_code)
            stock_name = stock_info.iloc[0]['name'] if not stock_info.empty else '未知'
        except:
            stock_name = '未知'
        
        print(f"股票名称: {stock_name}")
        
        # 获取行业分类
        industry_info = get_industry_info_improved(ts_code)
        
        result = {
            '股票代码': ts_code,
            '股票名称': stock_name,
            **industry_info
        }
        results.append(result)
        
        # 避免频率限制
        time.sleep(0.5)
    
    # 保存结果
    df = pd.DataFrame(results)
    filename = '申万行业分类测试结果.xlsx'
    df.to_excel(filename, index=False)
    print(f"\n测试结果已保存到: {filename}")
    
    # 显示汇总
    print("\n=== 测试汇总 ===")
    for result in results:
        print(f"{result['股票代码']} {result['股票名称']}: {result['申万一级行业']} > {result['申万二级行业']} > {result['申万三级行业']}")

def main():
    """主函数"""
    print("申万行业分类API测试")
    print("=" * 50)
    
    # 测试单只股票
    test_code = '000001.SZ'
    
    print(f"测试股票: {test_code}")
    print("-" * 30)
    
    # 分别测试新旧API
    old_result = test_industry_api_old(test_code)
    print()
    new_result = test_industry_api_new(test_code)
    print()
    
    # 测试改进的函数
    improved_result = get_industry_info_improved(test_code)
    print()
    
    # 测试多只股票
    choice = input("是否测试多只股票? (y/n): ").strip().lower()
    if choice == 'y':
        test_multiple_stocks()

if __name__ == "__main__":
    main()
