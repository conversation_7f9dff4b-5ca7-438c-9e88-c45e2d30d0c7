# 分时数据获取说明

## 基于您现有代码的简单方案

### 1. 使用您的下载脚本
您的 `下载日数据.py` 脚本已经设置为下载5分钟数据：
```python
period = '5m'  # 5分钟数据
```

### 2. 运行步骤
1. **下载数据**: 运行您的 `下载日数据.py` 脚本
2. **获取综合数据**: 运行 `获取股票数据_简化版.py`

### 3. 分时数据获取逻辑
改进后的代码会：
- 使用您下载的5分钟数据
- 自动查找最接近09:35、09:40、09:45的数据点
- 允许10分钟误差（因为是5分钟间隔数据）

### 4. 代码改进点
```python
def get_intraday_prices(ts_code, trade_date):
    """直接从5分钟数据获取09:35、09:40、09:45的收盘价"""
    # 使用与您下载脚本完全相同的参数
    data = xtdata.get_local_data(
        field_list=['time', 'open','close','high','low','volume','amount'],
        stock_list=[xt_code],
        period='5m',           # 使用5分钟数据
        dividend_type='front'  # 前复权
    )

    # 使用您的时间转换方式
    df['datetime'] = df['time'].apply(lambda x: datetime.datetime.fromtimestamp(x/1000.0))

    # 直接按时间筛选（5分钟数据正好有这些时间点）
    df['time_str'] = df['datetime'].dt.strftime('%H:%M')
    price_0935 = df[df['time_str'] == '09:35']['close'].iloc[0] if len(df[df['time_str'] == '09:35']) > 0 else None
    price_0940 = df[df['time_str'] == '09:40']['close'].iloc[0] if len(df[df['time_str'] == '09:40']) > 0 else None
    price_0945 = df[df['time_str'] == '09:45']['close'].iloc[0] if len(df[df['time_str'] == '09:45']) > 0 else None
```

### 5. 数据精度说明
- **5分钟数据**: 时间点为09:30, 09:35, 09:40, 09:45等
- **09:35**: 直接精确匹配时间戳
- **09:40**: 直接精确匹配时间戳
- **09:45**: 直接精确匹配时间戳
- **无需近似**: 直接使用5分钟K线的收盘价

### 6. 如果需要更精确的数据
如果您需要1分钟精度，只需修改下载脚本中的：
```python
period = '1m'  # 改为1分钟数据
```

### 7. 优势
- ✅ 复用您现有的下载逻辑
- ✅ 保持代码风格一致
- ✅ 简单可靠
- ✅ 不需要额外的复杂配置

### 8. 使用流程
```bash
# 1. 下载数据（使用您的脚本）
python 下载日数据.py

# 2. 获取综合数据（包含分时价格）
python 获取股票数据_简化版.py
```

这样就能获取到09:35、09:40、09:45三个时间点的收盘价了！
