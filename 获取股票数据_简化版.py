import tushare as ts
import pandas as pd
import time
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings("ignore")

# 初始化tushare pro
# 请替换为你的tushare token
TOKEN = '2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211'
pro = ts.pro_api(TOKEN)

def get_index_member_info(ts_code):
    """获取指数成分股信息"""
    index_codes = {
        '000300.SH': '沪深300',
        '000016.SH': '上证50',
        '000905.SH': '中证500',
        '000852.SH': '中证1000',
        '932000.CSI': '中证2000',
        '399006.SZ': '创业板指'
    }
    
    member_info = {}
    
    for index_code, index_name in index_codes.items():
        try:
            members = pro.index_weight(
                index_code=index_code,
                trade_date='',  # 最新数据
                fields='index_code,con_code'
            )
            if not members.empty and ts_code in members['con_code'].values:
                member_info[index_name] = 1
            else:
                member_info[index_name] = 0
            time.sleep(0.1)  # 避免频率限制
        except Exception as e:
            print(f"获取{index_name}成分股信息失败: {e}")
            member_info[index_name] = 0
    
    return member_info

def get_industry_info(ts_code):
    """获取申万行业分类信息 - 使用新版API"""
    try:
        # 使用新版申万行业分类API
        industry_data = pro.index_member_all(
            ts_code=ts_code,
            is_new='Y'  # 获取最新分类
        )

        industry_info = {
            '申万一级行业': '',
            '申万二级行业': '',
            '申万三级行业': ''
        }

        if not industry_data.empty:
            # 取第一条记录（最新的分类）
            row = industry_data.iloc[0]
            industry_info = {
                '申万一级行业': row.get('l1_name', ''),
                '申万二级行业': row.get('l2_name', ''),
                '申万三级行业': row.get('l3_name', '')
            }
            print(f"   申万行业分类: {industry_info['申万一级行业']} > {industry_info['申万二级行业']} > {industry_info['申万三级行业']}")

        return industry_info
    except Exception as e:
        print(f"获取申万行业信息失败: {e}")
        # 如果新API失败，尝试使用旧API作为备用
        try:
            print("   尝试使用备用API...")
            stock_industry = pro.stock_industry(
                ts_code=ts_code,
                fields='ts_code,industry_name,industry_code,level'
            )

            industry_info = {
                '申万一级行业': '',
                '申万二级行业': '',
                '申万三级行业': ''
            }

            if not stock_industry.empty:
                for _, row in stock_industry.iterrows():
                    if row['level'] == 'L1':
                        industry_info['申万一级行业'] = row['industry_name']
                    elif row['level'] == 'L2':
                        industry_info['申万二级行业'] = row['industry_name']
                    elif row['level'] == 'L3':
                        industry_info['申万三级行业'] = row['industry_name']

            return industry_info
        except Exception as e2:
            print(f"备用API也失败: {e2}")
            return {'申万一级行业': '', '申万二级行业': '', '申万三级行业': ''}

def get_stock_basic_info():
    """获取股票基础信息"""
    try:
        # 获取股票列表
        stock_basic = pro.stock_basic(
            exchange='',
            list_status='L',  # 上市状态：L上市 D退市 P暂停上市
            fields='ts_code,symbol,name,area,industry,market,list_date'
        )
        return stock_basic
    except Exception as e:
        print(f"获取股票基础信息失败: {e}")
        return None

def get_comprehensive_stock_data(ts_code, trade_date=None):
    """获取包含所有必需字段的股票数据（不包含分时数据）"""
    if trade_date is None:
        trade_date = datetime.now().strftime('%Y%m%d')
    
    print(f"正在获取股票 {ts_code} 的综合数据...")
    
    result = {
        '股票代码': ts_code,
        '股票名称': '',
        '交易日期': trade_date,
        '开盘价': None,
        '最高价': None,
        '最低价': None,
        '收盘价': None,
        '前收盘价': None,
        '成交量': None,
        '成交额': None,
        '流通市值': None,
        '总市值': None,
        '净利润TTM': None,
        '现金流TTM': None,
        '净资产': None,
        '总资产': None,
        '总负债': None,
        '净利润(当季)': None,
        '中户资金买入额': None,
        '中户资金卖出额': None,
        '大户资金买入额': None,
        '大户资金卖出额': None,
        '散户资金买入额': None,
        '散户资金卖出额': None,
        '机构资金买入额': None,
        '机构资金卖出额': None,
        '沪深300成分股': 0,
        '上证50成分股': 0,
        '中证500成分股': 0,
        '中证1000成分股': 0,
        '中证2000成分股': 0,
        '创业板指成分股': 0,
        '新版申万一级行业名称': '',
        '新版申万二级行业名称': '',
        '新版申万三级行业名称': '',
        '09:35收盘价': None,  # 暂时设为None，需要分时数据
        '09:45收盘价': None,  # 暂时设为None，需要分时数据
        '09:55收盘价': None   # 暂时设为None，需要分时数据
    }
    
    try:
        # 1. 获取基础信息
        stock_info = pro.stock_basic(ts_code=ts_code)
        if not stock_info.empty:
            result['股票名称'] = stock_info.iloc[0]['name']
        
        # 2. 获取日线行情数据
        price_data = pro.daily(
            ts_code=ts_code,
            trade_date=trade_date,
            fields='ts_code,trade_date,open,high,low,close,pre_close,vol,amount'
        )
        
        if not price_data.empty:
            row = price_data.iloc[0]
            result.update({
                '开盘价': row['open'],
                '最高价': row['high'],
                '最低价': row['low'],
                '收盘价': row['close'],
                '前收盘价': row['pre_close'],
                '成交量': row['vol'],
                '成交额': row['amount']
            })
        
        # 3. 获取市值数据
        daily_basic = pro.daily_basic(
            ts_code=ts_code,
            trade_date=trade_date,
            fields='ts_code,trade_date,circ_mv,total_mv'
        )
        
        if not daily_basic.empty:
            row = daily_basic.iloc[0]
            result.update({
                '流通市值': row['circ_mv'],
                '总市值': row['total_mv']
            })
        
        # 4. 获取财务数据 (最近4个季度)
        end_date = trade_date
        start_date = (datetime.strptime(trade_date, '%Y%m%d') - timedelta(days=365*2)).strftime('%Y%m%d')
        
        # 资产负债表
        try:
            balancesheet = pro.balancesheet(
                ts_code=ts_code,
                start_date=start_date,
                end_date=end_date,
                fields='ts_code,end_date,total_assets,total_liab,total_hldr_eqy_exc_min_int'
            )
            
            if not balancesheet.empty:
                latest = balancesheet.iloc[0]
                result.update({
                    '净资产': latest['total_hldr_eqy_exc_min_int'],
                    '总资产': latest['total_assets'],
                    '总负债': latest['total_liab']
                })
        except Exception as e:
            print(f"获取资产负债表失败: {e}")
        
        # 利润表
        try:
            income = pro.income(
                ts_code=ts_code,
                start_date=start_date,
                end_date=end_date,
                fields='ts_code,end_date,n_income_attr_p'
            )
            
            if not income.empty:
                # TTM计算
                recent_income = income.head(4)
                ttm_profit = recent_income['n_income_attr_p'].sum()
                result['净利润TTM'] = ttm_profit
                result['净利润(当季)'] = income.iloc[0]['n_income_attr_p']
        except Exception as e:
            print(f"获取利润表失败: {e}")
        
        # 现金流量表
        try:
            cashflow = pro.cashflow(
                ts_code=ts_code,
                start_date=start_date,
                end_date=end_date,
                fields='ts_code,end_date,n_cashflow_act'
            )
            
            if not cashflow.empty:
                recent_cashflow = cashflow.head(4)
                ttm_cashflow = recent_cashflow['n_cashflow_act'].sum()
                result['现金流TTM'] = ttm_cashflow
        except Exception as e:
            print(f"获取现金流量表失败: {e}")
        
        time.sleep(0.2)  # 避免频率限制
        
    except Exception as e:
        print(f"获取股票 {ts_code} 基础数据时出错: {e}")
    
    # 5. 获取资金流向数据
    try:
        moneyflow = pro.moneyflow(
            ts_code=ts_code,
            trade_date=trade_date,
            fields='ts_code,trade_date,buy_sm_amount,sell_sm_amount,buy_md_amount,sell_md_amount,buy_lg_amount,sell_lg_amount,buy_elg_amount,sell_elg_amount'
        )

        if not moneyflow.empty:
            row = moneyflow.iloc[0]
            result.update({
                '散户资金买入额': row['buy_sm_amount'],
                '散户资金卖出额': row['sell_sm_amount'],
                '中户资金买入额': row['buy_md_amount'],
                '中户资金卖出额': row['sell_md_amount'],
                '大户资金买入额': row['buy_lg_amount'],
                '大户资金卖出额': row['sell_lg_amount'],
                '机构资金买入额': row['buy_elg_amount'],
                '机构资金卖出额': row['sell_elg_amount']
            })
    except Exception as e:
        print(f"获取资金流向数据失败: {e}")

    # 6. 获取指数成分股信息
    try:
        index_member = get_index_member_info(ts_code)
        result.update({
            '沪深300成分股': index_member.get('沪深300', 0),
            '上证50成分股': index_member.get('上证50', 0),
            '中证500成分股': index_member.get('中证500', 0),
            '中证1000成分股': index_member.get('中证1000', 0),
            '中证2000成分股': index_member.get('中证2000', 0),
            '创业板指成分股': index_member.get('创业板指', 0)
        })
    except Exception as e:
        print(f"获取指数成分股信息失败: {e}")

    # 7. 获取行业信息
    try:
        industry_info = get_industry_info(ts_code)
        result.update({
            '新版申万一级行业名称': industry_info.get('申万一级行业', ''),
            '新版申万二级行业名称': industry_info.get('申万二级行业', ''),
            '新版申万三级行业名称': industry_info.get('申万三级行业', '')
        })
    except Exception as e:
        print(f"获取行业信息失败: {e}")

    return result

def get_batch_stocks_data(stock_list=None, trade_date=None, max_stocks=None):
    """批量获取股票数据"""
    if trade_date is None:
        trade_date = datetime.now().strftime('%Y%m%d')

    if stock_list is None:
        # 获取股票列表
        stock_basic = get_stock_basic_info()
        if stock_basic is None:
            return None
        stock_list = stock_basic['ts_code'].tolist()

    if max_stocks:
        stock_list = stock_list[:max_stocks]

    print(f"开始获取 {len(stock_list)} 只股票的数据，交易日期: {trade_date}")

    all_data = []
    total = len(stock_list)

    for i, ts_code in enumerate(stock_list):
        print(f"进度: {i+1}/{total} - 正在处理 {ts_code}")

        try:
            stock_data = get_comprehensive_stock_data(ts_code, trade_date)
            all_data.append(stock_data)

            # 避免频率限制
            time.sleep(0.3)

        except Exception as e:
            print(f"处理股票 {ts_code} 时出错: {e}")
            continue

        # 每10只股票显示进度
        if (i + 1) % 10 == 0:
            print(f"已完成 {i + 1}/{total} 只股票")

    if all_data:
        df = pd.DataFrame(all_data)
        print(f"成功获取 {len(df)} 只股票数据")
        return df
    else:
        print("未获取到任何数据")
        return None

def save_stock_data(df, filename=None):
    """保存股票数据到Excel"""
    if filename is None:
        trade_date = df['交易日期'].iloc[0] if '交易日期' in df.columns else datetime.now().strftime('%Y%m%d')
        filename = f'股票综合数据_{trade_date}.xlsx'

    try:
        # 确保字段顺序
        required_columns = [
            '股票代码', '股票名称', '交易日期', '开盘价', '最高价', '最低价', '收盘价', '前收盘价',
            '成交量', '成交额', '流通市值', '总市值', '净利润TTM', '现金流TTM', '净资产', '总资产',
            '总负债', '净利润(当季)', '中户资金买入额', '中户资金卖出额', '大户资金买入额', '大户资金卖出额',
            '散户资金买入额', '散户资金卖出额', '机构资金买入额', '机构资金卖出额', '沪深300成分股',
            '上证50成分股', '中证500成分股', '中证1000成分股', '中证2000成分股', '创业板指成分股',
            '新版申万一级行业名称', '新版申万二级行业名称', '新版申万三级行业名称',
            '09:35收盘价', '09:45收盘价', '09:55收盘价'
        ]

        # 重新排列列顺序
        df_ordered = df.reindex(columns=required_columns)

        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            df_ordered.to_excel(writer, sheet_name='股票数据', index=False)

            # 创建字段说明表
            field_descriptions = [
                ['字段名称', '说明'],
                ['股票代码', 'Tushare股票代码'],
                ['股票名称', '股票名称'],
                ['交易日期', '交易日期(YYYYMMDD)'],
                ['开盘价', '开盘价(元)'],
                ['最高价', '最高价(元)'],
                ['最低价', '最低价(元)'],
                ['收盘价', '收盘价(元)'],
                ['前收盘价', '前收盘价(元)'],
                ['成交量', '成交量(手)'],
                ['成交额', '成交额(千元)'],
                ['流通市值', '流通市值(万元)'],
                ['总市值', '总市值(万元)'],
                ['净利润TTM', '归母净利润TTM(万元)'],
                ['现金流TTM', '经营现金流TTM(万元)'],
                ['净资产', '归母净资产(万元)'],
                ['总资产', '总资产(万元)'],
                ['总负债', '总负债(万元)'],
                ['净利润(当季)', '最新季度归母净利润(万元)'],
                ['中户资金买入额', '中户资金买入额(万元)'],
                ['中户资金卖出额', '中户资金卖出额(万元)'],
                ['大户资金买入额', '大户资金买入额(万元)'],
                ['大户资金卖出额', '大户资金卖出额(万元)'],
                ['散户资金买入额', '散户资金买入额(万元)'],
                ['散户资金卖出额', '散户资金卖出额(万元)'],
                ['机构资金买入额', '机构资金买入额(万元)'],
                ['机构资金卖出额', '机构资金卖出额(万元)'],
                ['沪深300成分股', '是否为沪深300成分股(1是/0否)'],
                ['上证50成分股', '是否为上证50成分股(1是/0否)'],
                ['中证500成分股', '是否为中证500成分股(1是/0否)'],
                ['中证1000成分股', '是否为中证1000成分股(1是/0否)'],
                ['中证2000成分股', '是否为中证2000成分股(1是/0否)'],
                ['创业板指成分股', '是否为创业板指成分股(1是/0否)'],
                ['新版申万一级行业名称', '申万一级行业分类'],
                ['新版申万二级行业名称', '申万二级行业分类'],
                ['新版申万三级行业名称', '申万三级行业分类'],
                ['09:35收盘价', '09:35时点收盘价(元) - 需要分时数据'],
                ['09:45收盘价', '09:45时点收盘价(元) - 需要分时数据'],
                ['09:55收盘价', '09:55时点收盘价(元) - 需要分时数据']
            ]

            desc_df = pd.DataFrame(field_descriptions[1:], columns=field_descriptions[0])
            desc_df.to_excel(writer, sheet_name='字段说明', index=False)

        print(f"数据已保存到: {filename}")
        return filename

    except Exception as e:
        print(f"保存文件失败: {e}")
        return None

def main():
    """主函数"""
    print("=" * 60)
    print("股票综合数据获取工具 - 简化版")
    print("=" * 60)
    print("将获取以下字段:")
    print("✓ 基础信息: 股票代码、股票名称、交易日期")
    print("✓ 行情数据: 开盘价、最高价、最低价、收盘价、前收盘价、成交量、成交额")
    print("✓ 市值数据: 流通市值、总市值")
    print("✓ 财务数据: 净利润TTM、现金流TTM、净资产、总资产、总负债、净利润(当季)")
    print("✓ 资金流向: 各类资金买入卖出额")
    print("✓ 指数成分: 沪深300、上证50、中证500、中证1000、中证2000、创业板指")
    print("✓ 行业分类: 申万一二三级行业")
    print("✗ 分时数据: 09:35、09:45、09:55收盘价 (需要miniQMT数据)")
    print("=" * 60)

    # 检查token
    if TOKEN == 'your_tushare_token_here':
        print("请先设置你的tushare token!")
        print("注册地址: https://tushare.pro/register")
        return

    # 获取交易日期
    date_input = input("请输入交易日期 (格式: YYYY-MM-DD，回车使用今天): ").strip()
    if not date_input:
        trade_date = datetime.now().strftime('%Y%m%d')
        print(f"使用今天: {datetime.now().strftime('%Y-%m-%d')}")
    else:
        try:
            trade_date = datetime.strptime(date_input, '%Y-%m-%d').strftime('%Y%m%d')
        except:
            print("日期格式错误，使用今天")
            trade_date = datetime.now().strftime('%Y%m%d')

    print("\n选择执行模式:")
    print("1. 测试单只股票")
    print("2. 获取前10只股票")
    print("3. 获取指定数量股票")
    print("4. 获取全部股票")

    choice = input("请选择 (1/2/3/4): ").strip()

    if choice == '1':
        # 测试单只股票
        stock_code = input("请输入股票代码 (默认: 000001.SZ): ").strip()
        if not stock_code:
            stock_code = '000001.SZ'

        print(f"开始获取 {stock_code} 的数据...")
        result = get_comprehensive_stock_data(stock_code, trade_date)

        print(f"\n=== {result['股票名称']} ({result['股票代码']}) ===")
        print(f"交易日期: {result['交易日期']}")
        print(f"收盘价: {result['收盘价']}")
        print(f"总市值: {result['总市值']} 万元")
        print(f"净利润TTM: {result['净利润TTM']} 万元")
        print(f"申万一级行业: {result['新版申万一级行业名称']}")
        print(f"沪深300成分股: {'是' if result['沪深300成分股'] else '否'}")

        # 保存单只股票数据
        df = pd.DataFrame([result])
        filename = save_stock_data(df)

    elif choice in ['2', '3', '4']:
        if choice == '2':
            max_stocks = 10
        elif choice == '3':
            max_input = input("请输入要获取的股票数量: ").strip()
            try:
                max_stocks = int(max_input)
            except:
                print("输入无效，使用默认值50")
                max_stocks = 50
        else:  # choice == '4'
            print("警告: 获取全部股票数据需要很长时间")
            confirm = input("确认继续? (y/n): ").strip().lower()
            if confirm != 'y':
                print("已取消")
                return
            max_stocks = None

        print(f"开始批量获取股票数据，数量限制: {'全部' if max_stocks is None else max_stocks}")

        df = get_batch_stocks_data(
            trade_date=trade_date,
            max_stocks=max_stocks
        )

        if df is not None and len(df) > 0:
            print(f"成功获取 {len(df)} 只股票数据")

            # 显示统计信息
            print(f"\n数据统计:")
            print(f"有收盘价数据: {df['收盘价'].notna().sum()} 只")
            print(f"有市值数据: {df['总市值'].notna().sum()} 只")
            print(f"有财务数据: {df['净利润TTM'].notna().sum()} 只")
            print(f"有资金流向数据: {df['散户资金买入额'].notna().sum()} 只")

            # 保存数据
            filename = save_stock_data(df)
            if filename:
                print(f"数据已保存到: {filename}")
                print("包含工作表: 股票数据、字段说明")
        else:
            print("未获取到任何数据")

    else:
        print("无效选择")

if __name__ == "__main__":
    main()
