import tushare as ts
import pandas as pd
import time
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings("ignore")

# 初始化tushare pro
TOKEN = '2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211'
pro = ts.pro_api(TOKEN)

def get_index_member_info(ts_code):
    """获取指数成分股信息"""
    index_codes = {
        '000300.SH': '沪深300',
        '000016.SH': '上证50',
        '000905.SH': '中证500',
        '000852.SH': '中证1000',
        '932000.CSI': '中证2000',
        '399006.SZ': '创业板指'
    }
    
    member_info = {}
    
    for index_code, index_name in index_codes.items():
        try:
            members = pro.index_weight(
                index_code=index_code,
                trade_date='',  # 最新数据
                fields='index_code,con_code'
            )
            if not members.empty and ts_code in members['con_code'].values:
                member_info[index_name] = 1
            else:
                member_info[index_name] = 0
            time.sleep(0.1)  # 避免频率限制
        except Exception as e:
            print(f"获取{index_name}成分股信息失败: {e}")
            member_info[index_name] = 0
    
    return member_info

def get_industry_info(ts_code):
    """获取申万行业分类信息"""
    try:
        # 获取申万行业分类
        stock_industry = pro.stock_industry(
            ts_code=ts_code,
            fields='ts_code,industry_name,industry_code,level'
        )
        
        industry_info = {
            '申万一级行业': '',
            '申万二级行业': '',
            '申万三级行业': ''
        }
        
        if not stock_industry.empty:
            for _, row in stock_industry.iterrows():
                if row['level'] == 'L1':
                    industry_info['申万一级行业'] = row['industry_name']
                elif row['level'] == 'L2':
                    industry_info['申万二级行业'] = row['industry_name']
                elif row['level'] == 'L3':
                    industry_info['申万三级行业'] = row['industry_name']
        
        return industry_info
    except Exception as e:
        print(f"获取行业信息失败: {e}")
        return {'申万一级行业': '', '申万二级行业': '', '申万三级行业': ''}

def test_single_stock():
    """测试单只股票数据获取"""
    ts_code = '000001.SZ'  # 平安银行
    trade_date = '20241108'  # 使用一个确定的交易日

    print(f"测试股票: {ts_code}")
    print(f"交易日期: {trade_date}")
    print("=" * 50)

    # 测试tushare连接
    try:
        print("测试tushare连接...")
        test_data = pro.stock_basic(ts_code=ts_code)
        print(f"连接成功，获取到股票信息: {len(test_data)} 条")
    except Exception as e:
        print(f"tushare连接失败: {e}")
        return None
    
    result = {
        '股票代码': ts_code,
        '股票名称': '',
        '交易日期': trade_date,
        '开盘价': None,
        '最高价': None,
        '最低价': None,
        '收盘价': None,
        '前收盘价': None,
        '成交量': None,
        '成交额': None,
        '流通市值': None,
        '总市值': None,
        '净利润TTM': None,
        '现金流TTM': None,
        '净资产': None,
        '总资产': None,
        '总负债': None,
        '净利润(当季)': None,
        '中户资金买入额': None,
        '中户资金卖出额': None,
        '大户资金买入额': None,
        '大户资金卖出额': None,
        '散户资金买入额': None,
        '散户资金卖出额': None,
        '机构资金买入额': None,
        '机构资金卖出额': None,
        '沪深300成分股': 0,
        '上证50成分股': 0,
        '中证500成分股': 0,
        '中证1000成分股': 0,
        '中证2000成分股': 0,
        '创业板指成分股': 0,
        '新版申万一级行业名称': '',
        '新版申万二级行业名称': '',
        '新版申万三级行业名称': ''
    }
    
    try:
        # 1. 获取基础信息
        print("1. 获取基础信息...")
        stock_info = pro.stock_basic(ts_code=ts_code)
        if not stock_info.empty:
            result['股票名称'] = stock_info.iloc[0]['name']
            print(f"   股票名称: {result['股票名称']}")
        
        # 2. 获取日线行情数据
        print("2. 获取日线行情数据...")
        price_data = pro.daily(
            ts_code=ts_code,
            trade_date=trade_date,
            fields='ts_code,trade_date,open,high,low,close,pre_close,vol,amount'
        )
        
        if not price_data.empty:
            row = price_data.iloc[0]
            result.update({
                '开盘价': row['open'],
                '最高价': row['high'],
                '最低价': row['low'],
                '收盘价': row['close'],
                '前收盘价': row['pre_close'],
                '成交量': row['vol'],
                '成交额': row['amount']
            })
            print(f"   收盘价: {result['收盘价']}")
        
        # 3. 获取市值数据
        print("3. 获取市值数据...")
        daily_basic = pro.daily_basic(
            ts_code=ts_code,
            trade_date=trade_date,
            fields='ts_code,trade_date,circ_mv,total_mv'
        )
        
        if not daily_basic.empty:
            row = daily_basic.iloc[0]
            result.update({
                '流通市值': row['circ_mv'],
                '总市值': row['total_mv']
            })
            print(f"   总市值: {result['总市值']} 万元")
        
        # 4. 获取财务数据
        print("4. 获取财务数据...")
        end_date = trade_date
        start_date = (datetime.strptime(trade_date, '%Y%m%d') - timedelta(days=365*2)).strftime('%Y%m%d')
        
        # 资产负债表
        try:
            balancesheet = pro.balancesheet(
                ts_code=ts_code,
                start_date=start_date,
                end_date=end_date,
                fields='ts_code,end_date,total_assets,total_liab,total_hldr_eqy_exc_min_int'
            )
            
            if not balancesheet.empty:
                latest = balancesheet.iloc[0]
                result.update({
                    '净资产': latest['total_hldr_eqy_exc_min_int'],
                    '总资产': latest['total_assets'],
                    '总负债': latest['total_liab']
                })
                print(f"   总资产: {result['总资产']} 万元")
        except Exception as e:
            print(f"   获取资产负债表失败: {e}")
        
        # 利润表
        try:
            income = pro.income(
                ts_code=ts_code,
                start_date=start_date,
                end_date=end_date,
                fields='ts_code,end_date,n_income_attr_p'
            )
            
            if not income.empty:
                # TTM计算
                recent_income = income.head(4)
                ttm_profit = recent_income['n_income_attr_p'].sum()
                result['净利润TTM'] = ttm_profit
                result['净利润(当季)'] = income.iloc[0]['n_income_attr_p']
                print(f"   净利润TTM: {result['净利润TTM']} 万元")
        except Exception as e:
            print(f"   获取利润表失败: {e}")
        
        # 现金流量表
        try:
            cashflow = pro.cashflow(
                ts_code=ts_code,
                start_date=start_date,
                end_date=end_date,
                fields='ts_code,end_date,n_cashflow_act'
            )
            
            if not cashflow.empty:
                recent_cashflow = cashflow.head(4)
                ttm_cashflow = recent_cashflow['n_cashflow_act'].sum()
                result['现金流TTM'] = ttm_cashflow
                print(f"   现金流TTM: {result['现金流TTM']} 万元")
        except Exception as e:
            print(f"   获取现金流量表失败: {e}")
        
        # 5. 获取资金流向数据
        print("5. 获取资金流向数据...")
        try:
            moneyflow = pro.moneyflow(
                ts_code=ts_code,
                trade_date=trade_date,
                fields='ts_code,trade_date,buy_sm_amount,sell_sm_amount,buy_md_amount,sell_md_amount,buy_lg_amount,sell_lg_amount,buy_elg_amount,sell_elg_amount'
            )
            
            if not moneyflow.empty:
                row = moneyflow.iloc[0]
                result.update({
                    '散户资金买入额': row['buy_sm_amount'],
                    '散户资金卖出额': row['sell_sm_amount'],
                    '中户资金买入额': row['buy_md_amount'],
                    '中户资金卖出额': row['sell_md_amount'],
                    '大户资金买入额': row['buy_lg_amount'],
                    '大户资金卖出额': row['sell_lg_amount'],
                    '机构资金买入额': row['buy_elg_amount'],
                    '机构资金卖出额': row['sell_elg_amount']
                })
                print(f"   机构资金买入额: {result['机构资金买入额']} 万元")
        except Exception as e:
            print(f"   获取资金流向数据失败: {e}")
        
        # 6. 获取指数成分股信息
        print("6. 获取指数成分股信息...")
        try:
            index_member = get_index_member_info(ts_code)
            result.update({
                '沪深300成分股': index_member.get('沪深300', 0),
                '上证50成分股': index_member.get('上证50', 0),
                '中证500成分股': index_member.get('中证500', 0),
                '中证1000成分股': index_member.get('中证1000', 0),
                '中证2000成分股': index_member.get('中证2000', 0),
                '创业板指成分股': index_member.get('创业板指', 0)
            })
            print(f"   沪深300成分股: {'是' if result['沪深300成分股'] else '否'}")
        except Exception as e:
            print(f"   获取指数成分股信息失败: {e}")
        
        # 7. 获取行业信息
        print("7. 获取行业信息...")
        try:
            industry_info = get_industry_info(ts_code)
            result.update({
                '新版申万一级行业名称': industry_info.get('申万一级行业', ''),
                '新版申万二级行业名称': industry_info.get('申万二级行业', ''),
                '新版申万三级行业名称': industry_info.get('申万三级行业', '')
            })
            print(f"   申万一级行业: {result['新版申万一级行业名称']}")
        except Exception as e:
            print(f"   获取行业信息失败: {e}")
        
        print("\n" + "=" * 50)
        print("测试完成！数据获取成功")
        print("=" * 50)
        
        # 保存结果
        df = pd.DataFrame([result])
        filename = f'测试结果_{ts_code}_{trade_date}.xlsx'
        df.to_excel(filename, index=False)
        print(f"结果已保存到: {filename}")
        
        return result
        
    except Exception as e:
        print(f"测试过程中出错: {e}")
        return None

if __name__ == "__main__":
    test_single_stock()
