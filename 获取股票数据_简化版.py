import tushare as ts
import pandas as pd
import time
from datetime import datetime, timedelta
import warnings
import os
from concurrent.futures import ThreadPoolExecutor, as_completed
from threading import Lock
import threading
warnings.filterwarnings("ignore")

# 尝试导入xtquant，如果没有安装则跳过
try:
    from xtquant import xtdata
    XTQUANT_AVAILABLE = True
except ImportError:
    XTQUANT_AVAILABLE = False
    print("注意: xtquant未安装，无法获取分时数据")

# 初始化tushare pro
# 请替换为你的tushare token
TOKEN = '2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211'
pro = ts.pro_api(TOKEN)

# 线程安全的计数器
class ThreadSafeCounter:
    def __init__(self):
        self._value = 0
        self._lock = Lock()

    def increment(self):
        with self._lock:
            self._value += 1
            return self._value

    @property
    def value(self):
        with self._lock:
            return self._value

def get_intraday_prices(ts_code, trade_date):
    """直接从5分钟数据获取09:35、09:40、09:45的收盘价"""
    if not XTQUANT_AVAILABLE:
        return None, None, None

    try:
        # 转换股票代码格式 (tushare格式转xtquant格式)
        if ts_code.endswith('.SH'):
            xt_code = ts_code.replace('.SH', '.SS')
        elif ts_code.endswith('.SZ'):
            xt_code = ts_code.replace('.SZ', '.SZ')
        else:
            xt_code = ts_code

        # 使用与您下载脚本完全相同的参数
        data = xtdata.get_local_data(
            field_list=['time', 'open','close','high','low','volume','amount'],
            stock_list=[xt_code],
            period='5m',
            dividend_type='front'
        )

        if xt_code not in data or data[xt_code].empty:
            return None, None, None

        df = data[xt_code]
        # 使用您的时间转换方式
        df['datetime'] = df['time'].apply(lambda x: datetime.fromtimestamp(x/1000.0))

        # 筛选当日数据
        trade_date_obj = datetime.strptime(trade_date, '%Y%m%d').date()
        df = df[df['datetime'].dt.date == trade_date_obj]

        if df.empty:
            return None, None, None

        # 直接按时间筛选（5分钟数据正好有09:35、09:40、09:45）
        df['time_str'] = df['datetime'].dt.strftime('%H:%M')

        price_0935 = df[df['time_str'] == '09:35']['close'].iloc[0] if len(df[df['time_str'] == '09:35']) > 0 else None
        price_0940 = df[df['time_str'] == '09:40']['close'].iloc[0] if len(df[df['time_str'] == '09:40']) > 0 else None
        price_0945 = df[df['time_str'] == '09:45']['close'].iloc[0] if len(df[df['time_str'] == '09:45']) > 0 else None

        return price_0935, price_0940, price_0945

    except Exception as e:
        print(f"   获取{ts_code}分时数据失败: {e}")
        return None, None, None

def get_index_member_info(ts_code):
    """获取指数成分股信息"""
    index_codes = {
        '000300.SH': '沪深300',
        '000016.SH': '上证50',
        '000905.SH': '中证500',
        '000852.SH': '中证1000',
        '932000.CSI': '中证2000',
        '399006.SZ': '创业板指'
    }
    
    member_info = {}
    
    for index_code, index_name in index_codes.items():
        try:
            members = pro.index_weight(
                index_code=index_code,
                trade_date='',  # 最新数据
                fields='index_code,con_code'
            )
            if not members.empty and ts_code in members['con_code'].values:
                member_info[index_name] = 1
            else:
                member_info[index_name] = 0
            time.sleep(0.1)  # 避免频率限制
        except Exception as e:
            print(f"获取{index_name}成分股信息失败: {e}")
            member_info[index_name] = 0
    
    return member_info

def get_industry_info(ts_code):
    """获取申万行业分类信息 - 使用新版API"""
    try:
        # 使用新版申万行业分类API
        industry_data = pro.index_member_all(
            ts_code=ts_code,
            is_new='Y'  # 获取最新分类
        )

        industry_info = {
            '申万一级行业': '',
            '申万二级行业': '',
            '申万三级行业': ''
        }

        if not industry_data.empty:
            # 取第一条记录（最新的分类）
            row = industry_data.iloc[0]
            industry_info = {
                '申万一级行业': row.get('l1_name', ''),
                '申万二级行业': row.get('l2_name', ''),
                '申万三级行业': row.get('l3_name', '')
            }
            print(f"   申万行业分类: {industry_info['申万一级行业']} > {industry_info['申万二级行业']} > {industry_info['申万三级行业']}")

        return industry_info
    except Exception as e:
        print(f"获取申万行业信息失败: {e}")
        # 如果新API失败，尝试使用旧API作为备用
        try:
            print("   尝试使用备用API...")
            stock_industry = pro.stock_industry(
                ts_code=ts_code,
                fields='ts_code,industry_name,industry_code,level'
            )

            industry_info = {
                '申万一级行业': '',
                '申万二级行业': '',
                '申万三级行业': ''
            }

            if not stock_industry.empty:
                for _, row in stock_industry.iterrows():
                    if row['level'] == 'L1':
                        industry_info['申万一级行业'] = row['industry_name']
                    elif row['level'] == 'L2':
                        industry_info['申万二级行业'] = row['industry_name']
                    elif row['level'] == 'L3':
                        industry_info['申万三级行业'] = row['industry_name']

            return industry_info
        except Exception as e2:
            print(f"备用API也失败: {e2}")
            return {'申万一级行业': '', '申万二级行业': '', '申万三级行业': ''}

def get_stock_basic_info():
    """获取股票基础信息"""
    try:
        # 获取股票列表
        stock_basic = pro.stock_basic(
            exchange='',
            list_status='L',  # 上市状态：L上市 D退市 P暂停上市
            fields='ts_code,symbol,name,area,industry,market,list_date'
        )
        return stock_basic
    except Exception as e:
        print(f"获取股票基础信息失败: {e}")
        return None

def get_comprehensive_stock_data(ts_code, trade_date=None):
    """获取包含所有必需字段的股票数据（不包含分时数据）"""
    if trade_date is None:
        trade_date = datetime.now().strftime('%Y%m%d')
    
    print(f"正在获取股票 {ts_code} 的综合数据...")
    
    result = {
        '股票代码': ts_code,
        '股票名称': '',
        '交易日期': trade_date,
        '开盘价': None,
        '最高价': None,
        '最低价': None,
        '收盘价': None,
        '前收盘价': None,
        '成交量': None,
        '成交额': None,
        '流通市值': None,
        '总市值': None,
        '净利润TTM': None,
        '现金流TTM': None,
        '净资产': None,
        '总资产': None,
        '总负债': None,
        '净利润(当季)': None,
        '中户资金买入额': None,
        '中户资金卖出额': None,
        '大户资金买入额': None,
        '大户资金卖出额': None,
        '散户资金买入额': None,
        '散户资金卖出额': None,
        '机构资金买入额': None,
        '机构资金卖出额': None,
        '沪深300成分股': 0,
        '上证50成分股': 0,
        '中证500成分股': 0,
        '中证1000成分股': 0,
        '中证2000成分股': 0,
        '创业板指成分股': 0,
        '新版申万一级行业名称': '',
        '新版申万二级行业名称': '',
        '新版申万三级行业名称': '',
        '09:35收盘价': None,  # 需要分时数据
        '09:40收盘价': None,  # 需要分时数据
        '09:45收盘价': None   # 需要分时数据
    }
    
    try:
        # 1. 获取基础信息
        stock_info = pro.stock_basic(ts_code=ts_code)
        if not stock_info.empty:
            result['股票名称'] = stock_info.iloc[0]['name']
        
        # 2. 获取日线行情数据
        price_data = pro.daily(
            ts_code=ts_code,
            trade_date=trade_date,
            fields='ts_code,trade_date,open,high,low,close,pre_close,vol,amount'
        )
        
        if not price_data.empty:
            row = price_data.iloc[0]
            result.update({
                '开盘价': row['open'],
                '最高价': row['high'],
                '最低价': row['low'],
                '收盘价': row['close'],
                '前收盘价': row['pre_close'],
                '成交量': row['vol'],
                '成交额': row['amount']
            })
        
        # 3. 获取市值数据
        daily_basic = pro.daily_basic(
            ts_code=ts_code,
            trade_date=trade_date,
            fields='ts_code,trade_date,circ_mv,total_mv'
        )
        
        if not daily_basic.empty:
            row = daily_basic.iloc[0]
            result.update({
                '流通市值': row['circ_mv'],
                '总市值': row['total_mv']
            })
        
        # 4. 获取财务数据 (最近4个季度)
        end_date = trade_date
        start_date = (datetime.strptime(trade_date, '%Y%m%d') - timedelta(days=365*2)).strftime('%Y%m%d')
        
        # 资产负债表
        try:
            balancesheet = pro.balancesheet(
                ts_code=ts_code,
                start_date=start_date,
                end_date=end_date,
                fields='ts_code,end_date,total_assets,total_liab,total_hldr_eqy_exc_min_int'
            )
            
            if not balancesheet.empty:
                latest = balancesheet.iloc[0]
                result.update({
                    '净资产': latest['total_hldr_eqy_exc_min_int'],
                    '总资产': latest['total_assets'],
                    '总负债': latest['total_liab']
                })
        except Exception as e:
            print(f"获取资产负债表失败: {e}")
        
        # 利润表
        try:
            income = pro.income(
                ts_code=ts_code,
                start_date=start_date,
                end_date=end_date,
                fields='ts_code,end_date,n_income_attr_p'
            )
            
            if not income.empty:
                # TTM计算
                recent_income = income.head(4)
                ttm_profit = recent_income['n_income_attr_p'].sum()
                result['净利润TTM'] = ttm_profit
                result['净利润(当季)'] = income.iloc[0]['n_income_attr_p']
        except Exception as e:
            print(f"获取利润表失败: {e}")
        
        # 现金流量表
        try:
            cashflow = pro.cashflow(
                ts_code=ts_code,
                start_date=start_date,
                end_date=end_date,
                fields='ts_code,end_date,n_cashflow_act'
            )
            
            if not cashflow.empty:
                recent_cashflow = cashflow.head(4)
                ttm_cashflow = recent_cashflow['n_cashflow_act'].sum()
                result['现金流TTM'] = ttm_cashflow
        except Exception as e:
            print(f"获取现金流量表失败: {e}")
        
        time.sleep(0.2)  # 避免频率限制
        
    except Exception as e:
        print(f"获取股票 {ts_code} 基础数据时出错: {e}")
    
    # 5. 获取资金流向数据
    try:
        moneyflow = pro.moneyflow(
            ts_code=ts_code,
            trade_date=trade_date,
            fields='ts_code,trade_date,buy_sm_amount,sell_sm_amount,buy_md_amount,sell_md_amount,buy_lg_amount,sell_lg_amount,buy_elg_amount,sell_elg_amount'
        )

        if not moneyflow.empty:
            row = moneyflow.iloc[0]
            result.update({
                '散户资金买入额': row['buy_sm_amount'],
                '散户资金卖出额': row['sell_sm_amount'],
                '中户资金买入额': row['buy_md_amount'],
                '中户资金卖出额': row['sell_md_amount'],
                '大户资金买入额': row['buy_lg_amount'],
                '大户资金卖出额': row['sell_lg_amount'],
                '机构资金买入额': row['buy_elg_amount'],
                '机构资金卖出额': row['sell_elg_amount']
            })
    except Exception as e:
        print(f"获取资金流向数据失败: {e}")

    # 6. 获取指数成分股信息
    try:
        index_member = get_index_member_info(ts_code)
        result.update({
            '沪深300成分股': index_member.get('沪深300', 0),
            '上证50成分股': index_member.get('上证50', 0),
            '中证500成分股': index_member.get('中证500', 0),
            '中证1000成分股': index_member.get('中证1000', 0),
            '中证2000成分股': index_member.get('中证2000', 0),
            '创业板指成分股': index_member.get('创业板指', 0)
        })
    except Exception as e:
        print(f"获取指数成分股信息失败: {e}")

    # 7. 获取行业信息
    try:
        industry_info = get_industry_info(ts_code)
        result.update({
            '新版申万一级行业名称': industry_info.get('申万一级行业', ''),
            '新版申万二级行业名称': industry_info.get('申万二级行业', ''),
            '新版申万三级行业名称': industry_info.get('申万三级行业', '')
        })
    except Exception as e:
        print(f"获取行业信息失败: {e}")

    # 8. 获取分时数据
    try:
        print("8. 获取分时数据...")

        # 先尝试下载5分钟数据（如果本地没有）
        if XTQUANT_AVAILABLE:
            try:
                # 转换股票代码格式用于下载
                if ts_code.endswith('.SH'):
                    xt_code = ts_code.replace('.SH', '.SS')
                elif ts_code.endswith('.SZ'):
                    xt_code = ts_code.replace('.SZ', '.SZ')
                else:
                    xt_code = ts_code

                print(f"   下载{xt_code}的5分钟数据...")
                xtdata.download_history_data(stock_code=xt_code, period='5m')
            except Exception as e:
                print(f"   下载5分钟数据失败: {e}")

        # 获取分时价格
        price_0935, price_0940, price_0945 = get_intraday_prices(ts_code, trade_date)
        result.update({
            '09:35收盘价': price_0935,
            '09:40收盘价': price_0940,
            '09:45收盘价': price_0945
        })

        if price_0935 is not None:
            print(f"   09:35收盘价: {price_0935}")
        if price_0940 is not None:
            print(f"   09:40收盘价: {price_0940}")
        if price_0945 is not None:
            print(f"   09:45收盘价: {price_0945}")
        else:
            print(f"   未获取到分时数据，可能需要先运行下载脚本")

    except Exception as e:
        print(f"   获取分时数据失败: {e}")

    return result

def get_single_stock_data_thread_safe(ts_code, trade_date, counter, total):
    """线程安全的单只股票数据获取"""
    try:
        stock_data = get_comprehensive_stock_data(ts_code, trade_date)

        # 更新进度
        current = counter.increment()
        if current % 10 == 0 or current == total:
            print(f"进度: {current}/{total} - 已完成 {ts_code}")

        # 避免频率限制
        time.sleep(0.1)

        return stock_data
    except Exception as e:
        print(f"处理股票 {ts_code} 时出错: {e}")
        return None

def get_batch_stocks_data(stock_list=None, trade_date=None, max_stocks=None, max_workers=8):
    """批量获取股票数据 - 多线程版本"""
    if trade_date is None:
        trade_date = datetime.now().strftime('%Y%m%d')

    if stock_list is None:
        # 获取股票列表
        stock_basic = get_stock_basic_info()
        if stock_basic is None:
            return None
        stock_list = stock_basic['ts_code'].tolist()

    if max_stocks:
        stock_list = stock_list[:max_stocks]

    total = len(stock_list)
    print(f"开始获取 {total} 只股票的数据，交易日期: {trade_date}")
    print(f"使用 {max_workers} 个线程并行处理")

    all_data = []
    counter = ThreadSafeCounter()

    # 使用线程池
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # 提交所有任务
        future_to_stock = {
            executor.submit(get_single_stock_data_thread_safe, ts_code, trade_date, counter, total): ts_code
            for ts_code in stock_list
        }

        # 收集结果
        for future in as_completed(future_to_stock):
            ts_code = future_to_stock[future]
            try:
                result = future.result()
                if result is not None:
                    all_data.append(result)
            except Exception as e:
                print(f"线程处理 {ts_code} 时出错: {e}")

    if all_data:
        df = pd.DataFrame(all_data)
        print(f"成功获取 {len(df)} 只股票数据")
        return df
    else:
        print("未获取到任何数据")
        return None

def get_batch_stocks_data_single_thread(stock_list=None, trade_date=None, max_stocks=None):
    """批量获取股票数据 - 单线程版本（备用）"""
    if trade_date is None:
        trade_date = datetime.now().strftime('%Y%m%d')

    if stock_list is None:
        # 获取股票列表
        stock_basic = get_stock_basic_info()
        if stock_basic is None:
            return None
        stock_list = stock_basic['ts_code'].tolist()

    if max_stocks:
        stock_list = stock_list[:max_stocks]

    print(f"开始获取 {len(stock_list)} 只股票的数据，交易日期: {trade_date}")

    all_data = []
    total = len(stock_list)

    for i, ts_code in enumerate(stock_list):
        print(f"进度: {i+1}/{total} - 正在处理 {ts_code}")

        try:
            stock_data = get_comprehensive_stock_data(ts_code, trade_date)
            all_data.append(stock_data)

            # 避免频率限制
            time.sleep(0.3)

        except Exception as e:
            print(f"处理股票 {ts_code} 时出错: {e}")
            continue

        # 每10只股票显示进度
        if (i + 1) % 10 == 0:
            print(f"已完成 {i + 1}/{total} 只股票")

    if all_data:
        df = pd.DataFrame(all_data)
        print(f"成功获取 {len(df)} 只股票数据")
        return df
    else:
        print("未获取到任何数据")
        return None

def get_batch_stocks_data_multi_dates(stock_list=None, trade_dates=None, max_stocks=None, max_workers=8):
    """批量获取多个交易日的股票数据"""
    if not trade_dates:
        trade_dates = [datetime.now().strftime('%Y%m%d')]

    if stock_list is None:
        # 获取股票列表
        stock_basic = get_stock_basic_info()
        if stock_basic is None:
            return None
        stock_list = stock_basic['ts_code'].tolist()

    if max_stocks:
        stock_list = stock_list[:max_stocks]

    print(f"开始获取 {len(stock_list)} 只股票在 {len(trade_dates)} 个交易日的数据")
    print(f"总计需要处理: {len(stock_list) * len(trade_dates)} 条记录")

    all_data = []
    counter = ThreadSafeCounter()
    total_tasks = len(stock_list) * len(trade_dates)

    # 创建所有任务
    tasks = []
    for trade_date in trade_dates:
        for ts_code in stock_list:
            tasks.append((ts_code, trade_date))

    # 使用线程池处理所有任务
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        future_to_task = {
            executor.submit(get_single_stock_data_thread_safe, ts_code, trade_date, counter, total_tasks): (ts_code, trade_date)
            for ts_code, trade_date in tasks
        }

        # 收集结果
        for future in as_completed(future_to_task):
            ts_code, trade_date = future_to_task[future]
            try:
                result = future.result()
                if result is not None:
                    all_data.append(result)
            except Exception as e:
                print(f"处理 {ts_code} - {trade_date} 时出错: {e}")

    if all_data:
        df = pd.DataFrame(all_data)
        print(f"成功获取 {len(df)} 条记录")
        return df
    else:
        print("未获取到任何数据")
        return None

def save_stock_data(df, filename=None):
    """保存股票数据到Excel"""
    if filename is None:
        trade_date = df['交易日期'].iloc[0] if '交易日期' in df.columns else datetime.now().strftime('%Y%m%d')
        filename = f'股票综合数据_{trade_date}.xlsx'

    try:
        # 确保字段顺序
        required_columns = [
            '股票代码', '股票名称', '交易日期', '开盘价', '最高价', '最低价', '收盘价', '前收盘价',
            '成交量', '成交额', '流通市值', '总市值', '净利润TTM', '现金流TTM', '净资产', '总资产',
            '总负债', '净利润(当季)', '中户资金买入额', '中户资金卖出额', '大户资金买入额', '大户资金卖出额',
            '散户资金买入额', '散户资金卖出额', '机构资金买入额', '机构资金卖出额', '沪深300成分股',
            '上证50成分股', '中证500成分股', '中证1000成分股', '中证2000成分股', '创业板指成分股',
            '新版申万一级行业名称', '新版申万二级行业名称', '新版申万三级行业名称',
            '09:35收盘价', '09:40收盘价', '09:45收盘价'
        ]

        # 重新排列列顺序
        df_ordered = df.reindex(columns=required_columns)

        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            df_ordered.to_excel(writer, sheet_name='股票数据', index=False)

            # 创建字段说明表
            field_descriptions = [
                ['字段名称', '说明'],
                ['股票代码', 'Tushare股票代码'],
                ['股票名称', '股票名称'],
                ['交易日期', '交易日期(YYYYMMDD)'],
                ['开盘价', '开盘价(元)'],
                ['最高价', '最高价(元)'],
                ['最低价', '最低价(元)'],
                ['收盘价', '收盘价(元)'],
                ['前收盘价', '前收盘价(元)'],
                ['成交量', '成交量(手)'],
                ['成交额', '成交额(千元)'],
                ['流通市值', '流通市值(万元)'],
                ['总市值', '总市值(万元)'],
                ['净利润TTM', '归母净利润TTM(万元)'],
                ['现金流TTM', '经营现金流TTM(万元)'],
                ['净资产', '归母净资产(万元)'],
                ['总资产', '总资产(万元)'],
                ['总负债', '总负债(万元)'],
                ['净利润(当季)', '最新季度归母净利润(万元)'],
                ['中户资金买入额', '中户资金买入额(万元)'],
                ['中户资金卖出额', '中户资金卖出额(万元)'],
                ['大户资金买入额', '大户资金买入额(万元)'],
                ['大户资金卖出额', '大户资金卖出额(万元)'],
                ['散户资金买入额', '散户资金买入额(万元)'],
                ['散户资金卖出额', '散户资金卖出额(万元)'],
                ['机构资金买入额', '机构资金买入额(万元)'],
                ['机构资金卖出额', '机构资金卖出额(万元)'],
                ['沪深300成分股', '是否为沪深300成分股(1是/0否)'],
                ['上证50成分股', '是否为上证50成分股(1是/0否)'],
                ['中证500成分股', '是否为中证500成分股(1是/0否)'],
                ['中证1000成分股', '是否为中证1000成分股(1是/0否)'],
                ['中证2000成分股', '是否为中证2000成分股(1是/0否)'],
                ['创业板指成分股', '是否为创业板指成分股(1是/0否)'],
                ['新版申万一级行业名称', '申万一级行业分类'],
                ['新版申万二级行业名称', '申万二级行业分类'],
                ['新版申万三级行业名称', '申万三级行业分类'],
                ['09:35收盘价', '09:35时点收盘价(元)'],
                ['09:40收盘价', '09:40时点收盘价(元)'],
                ['09:45收盘价', '09:45时点收盘价(元)']
            ]

            desc_df = pd.DataFrame(field_descriptions[1:], columns=field_descriptions[0])
            desc_df.to_excel(writer, sheet_name='字段说明', index=False)

        print(f"数据已保存到: {filename}")
        return filename

    except Exception as e:
        print(f"保存文件失败: {e}")
        return None

def main():
    """主函数"""
    print("=" * 60)
    print("股票综合数据获取工具 - 简化版")
    print("=" * 60)
    print("将获取以下字段:")
    print("✓ 基础信息: 股票代码、股票名称、交易日期")
    print("✓ 行情数据: 开盘价、最高价、最低价、收盘价、前收盘价、成交量、成交额")
    print("✓ 市值数据: 流通市值、总市值")
    print("✓ 财务数据: 净利润TTM、现金流TTM、净资产、总资产、总负债、净利润(当季)")
    print("✓ 资金流向: 各类资金买入卖出额")
    print("✓ 指数成分: 沪深300、上证50、中证500、中证1000、中证2000、创业板指")
    print("✓ 行业分类: 申万一二三级行业")
    print("✓ 分时数据: 09:35、09:40、09:45收盘价 (多种获取方式)")
    print("=" * 60)

    # 检查token
    if TOKEN == 'your_tushare_token_here':
        print("请先设置你的tushare token!")
        print("注册地址: https://tushare.pro/register")
        return

    # 选择日期模式
    print("\n选择日期模式:")
    print("1. 单个交易日")
    print("2. 时间范围")

    date_mode = input("请选择 (1/2，默认1): ").strip()

    trade_dates = []

    if date_mode == '2':
        # 时间范围模式
        print("\n时间范围模式:")
        start_date_input = input("请输入开始日期 (格式: YYYY-MM-DD): ").strip()
        end_date_input = input("请输入结束日期 (格式: YYYY-MM-DD，回车使用今天): ").strip()

        try:
            start_date = datetime.strptime(start_date_input, '%Y-%m-%d')

            if not end_date_input:
                end_date = datetime.now()
            else:
                end_date = datetime.strptime(end_date_input, '%Y-%m-%d')

            # 确保开始日期不晚于结束日期
            if start_date > end_date:
                start_date, end_date = end_date, start_date
                print("注意: 开始日期晚于结束日期，已自动交换")

            # 生成日期范围（只包含工作日）
            current_date = start_date
            while current_date <= end_date:
                # 只包含工作日（周一到周五）
                if current_date.weekday() < 5:
                    trade_dates.append(current_date.strftime('%Y%m%d'))
                current_date += timedelta(days=1)

            print(f"时间范围: {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')}")
            print(f"包含 {len(trade_dates)} 个交易日")

            # 显示前几个和后几个日期
            if len(trade_dates) > 6:
                preview_dates = trade_dates[:3] + ['...'] + trade_dates[-3:]
                print(f"日期预览: {', '.join(preview_dates)}")
            else:
                print(f"交易日期: {', '.join(trade_dates)}")

            confirm = input("确认处理这些日期? (y/n): ").strip().lower()
            if confirm != 'y':
                print("已取消")
                return

        except ValueError:
            print("日期格式错误，使用单日模式")
            date_mode = '1'

    if date_mode != '2' or not trade_dates:
        # 单个交易日模式
        date_input = input("请输入交易日期 (格式: YYYY-MM-DD，回车使用今天): ").strip()
        if not date_input:
            trade_date = datetime.now().strftime('%Y%m%d')
            print(f"使用今天: {datetime.now().strftime('%Y-%m-%d')}")
        else:
            try:
                trade_date = datetime.strptime(date_input, '%Y-%m-%d').strftime('%Y%m%d')
            except:
                print("日期格式错误，使用今天")
                trade_date = datetime.now().strftime('%Y%m%d')

        trade_dates = [trade_date]

    print("\n选择执行模式:")
    print("1. 测试单只股票")
    print("2. 获取前10只股票")
    print("3. 获取指定数量股票")
    print("4. 获取全部股票")
    print("5. 先下载5分钟数据，再获取综合数据")

    choice = input("请选择 (1/2/3/4/5): ").strip()

    # 如果是批量处理，询问是否使用多线程
    use_multithread = False
    max_workers = 8
    if choice in ['2', '3', '4', '5']:
        thread_choice = input("是否使用多线程加速? (y/n，默认y): ").strip().lower()
        if thread_choice != 'n':
            use_multithread = True
            worker_input = input("请输入线程数 (默认8，建议4-16): ").strip()
            if worker_input.isdigit():
                max_workers = int(worker_input)
                max_workers = max(1, min(max_workers, 32))  # 限制在1-32之间
            print(f"将使用 {max_workers} 个线程并行处理")
        else:
            print("将使用单线程处理")

    if choice == '1':
        # 测试单只股票
        stock_code = input("请输入股票代码 (默认: 000001.SZ): ").strip()
        if not stock_code:
            stock_code = '000001.SZ'

        all_results = []

        for i, trade_date in enumerate(trade_dates):
            print(f"\n[{i+1}/{len(trade_dates)}] 获取 {stock_code} 在 {trade_date} 的数据...")
            result = get_comprehensive_stock_data(stock_code, trade_date)
            all_results.append(result)

            print(f"=== {result['股票名称']} ({result['股票代码']}) - {trade_date} ===")
            print(f"收盘价: {result['收盘价']}")
            print(f"总市值: {result['总市值']} 万元")
            print(f"09:35收盘价: {result['09:35收盘价']}")
            print(f"09:40收盘价: {result['09:40收盘价']}")
            print(f"09:45收盘价: {result['09:45收盘价']}")

        # 保存所有数据
        df = pd.DataFrame(all_results)
        filename = save_stock_data(df)
        print(f"\n共获取 {len(all_results)} 条记录")

    elif choice == '5':
        # 先下载5分钟数据，再获取综合数据
        if not XTQUANT_AVAILABLE:
            print("错误: 需要安装xtquant才能下载数据")
            return

        max_input = input("请输入要处理的股票数量 (回车处理全部): ").strip()
        if max_input.isdigit():
            max_stocks = int(max_input)
        else:
            max_stocks = None

        # 获取股票列表
        stock_basic = get_stock_basic_info()
        if stock_basic is None:
            return
        stock_list = stock_basic['ts_code'].tolist()

        if max_stocks:
            stock_list = stock_list[:max_stocks]

        print(f"开始处理 {len(stock_list)} 只股票")

        # 第一步：批量下载5分钟数据
        print("\n第一步：批量下载5分钟数据...")
        from joblib import Parallel, delayed

        def download_data(stock):
            try:
                # 转换股票代码格式
                if stock.endswith('.SH'):
                    xt_code = stock.replace('.SH', '.SS')
                elif stock.endswith('.SZ'):
                    xt_code = stock.replace('.SZ', '.SZ')
                else:
                    xt_code = stock

                xtdata.download_history_data(stock_code=xt_code, period='5m')
            except:
                print(f'{stock} 下载失败')

        Parallel(n_jobs=4)(delayed(download_data)(stock) for stock in stock_list)
        print('5分钟数据下载完成')

        # 第二步：获取综合数据
        print("\n第二步：获取综合数据...")
        if len(trade_dates) == 1:
            # 单日期处理
            if use_multithread:
                df = get_batch_stocks_data(
                    stock_list=stock_list,
                    trade_date=trade_dates[0],
                    max_stocks=None,
                    max_workers=max_workers
                )
            else:
                df = get_batch_stocks_data_single_thread(
                    stock_list=stock_list,
                    trade_date=trade_dates[0],
                    max_stocks=None
                )
        else:
            # 多日期处理
            if use_multithread:
                df = get_batch_stocks_data_multi_dates(
                    stock_list=stock_list,
                    trade_dates=trade_dates,
                    max_stocks=None,
                    max_workers=max_workers
                )
            else:
                # 多日期单线程处理
                all_data = []
                for i, trade_date in enumerate(trade_dates):
                    print(f"\n处理日期 {i+1}/{len(trade_dates)}: {trade_date}")
                    daily_df = get_batch_stocks_data_single_thread(
                        stock_list=stock_list,
                        trade_date=trade_date,
                        max_stocks=None
                    )
                    if daily_df is not None:
                        all_data.append(daily_df)

                if all_data:
                    df = pd.concat(all_data, ignore_index=True)
                else:
                    df = None

        if df is not None and len(df) > 0:
            print(f"成功获取 {len(df)} 只股票数据")

            # 显示统计信息
            print(f"\n数据统计:")
            print(f"有收盘价数据: {df['收盘价'].notna().sum()} 只")
            print(f"有市值数据: {df['总市值'].notna().sum()} 只")
            print(f"有财务数据: {df['净利润TTM'].notna().sum()} 只")
            print(f"有分时数据: {df['09:35收盘价'].notna().sum()} 只")

            # 保存数据
            filename = save_stock_data(df)
            if filename:
                print(f"数据已保存到: {filename}")
        else:
            print("未获取到任何数据")

    elif choice in ['2', '3', '4']:
        if choice == '2':
            max_stocks = 10
        elif choice == '3':
            max_input = input("请输入要获取的股票数量: ").strip()
            try:
                max_stocks = int(max_input)
            except:
                print("输入无效，使用默认值50")
                max_stocks = 50
        else:  # choice == '4'
            print("警告: 获取全部股票数据需要很长时间")
            confirm = input("确认继续? (y/n): ").strip().lower()
            if confirm != 'y':
                print("已取消")
                return
            max_stocks = None

        print(f"开始批量获取股票数据，数量限制: {'全部' if max_stocks is None else max_stocks}")
        print(f"交易日期: {len(trade_dates)} 个")

        if len(trade_dates) == 1:
            # 单日期处理
            if use_multithread:
                df = get_batch_stocks_data(
                    trade_date=trade_dates[0],
                    max_stocks=max_stocks,
                    max_workers=max_workers
                )
            else:
                df = get_batch_stocks_data_single_thread(
                    trade_date=trade_dates[0],
                    max_stocks=max_stocks
                )
        else:
            # 多日期处理
            if use_multithread:
                df = get_batch_stocks_data_multi_dates(
                    trade_dates=trade_dates,
                    max_stocks=max_stocks,
                    max_workers=max_workers
                )
            else:
                # 多日期单线程处理
                all_data = []
                for i, trade_date in enumerate(trade_dates):
                    print(f"\n处理日期 {i+1}/{len(trade_dates)}: {trade_date}")
                    daily_df = get_batch_stocks_data_single_thread(
                        trade_date=trade_date,
                        max_stocks=max_stocks
                    )
                    if daily_df is not None:
                        all_data.append(daily_df)

                if all_data:
                    df = pd.concat(all_data, ignore_index=True)
                else:
                    df = None

        if df is not None and len(df) > 0:
            print(f"成功获取 {len(df)} 只股票数据")

            # 显示统计信息
            print(f"\n数据统计:")
            print(f"有收盘价数据: {df['收盘价'].notna().sum()} 只")
            print(f"有市值数据: {df['总市值'].notna().sum()} 只")
            print(f"有财务数据: {df['净利润TTM'].notna().sum()} 只")
            print(f"有资金流向数据: {df['散户资金买入额'].notna().sum()} 只")

            # 保存数据
            filename = save_stock_data(df)
            if filename:
                print(f"数据已保存到: {filename}")
                print("包含工作表: 股票数据、字段说明")
        else:
            print("未获取到任何数据")

    else:
        print("无效选择")

if __name__ == "__main__":
    main()
