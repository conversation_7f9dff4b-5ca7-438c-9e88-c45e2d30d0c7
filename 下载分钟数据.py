import tushare as ts
import pandas as pd
import time
from datetime import datetime, timedelta

# 初始化tushare pro
# 请替换为你的tushare token
TOKEN = '2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211'
pro = ts.pro_api(TOKEN)

def get_stock_basic_info():
    """获取股票基础信息"""
    try:
        # 获取股票列表
        stock_basic = pro.stock_basic(
            exchange='',
            list_status='L',  # 上市状态：L上市 D退市 P暂停上市
            fields='ts_code,symbol,name,area,industry,market,list_date'
        )
        return stock_basic
    except Exception as e:
        print(f"获取股票基础信息失败: {e}")
        return None

def get_financial_data_range(ts_code, start_date=None, end_date=None):
    """获取指定时间范围内的财务数据"""
    if end_date is None:
        end_date = datetime.now().strftime('%Y%m%d')
    if start_date is None:
        # 默认获取过去4个季度的数据
        start_date = (datetime.now() - timedelta(days=365)).strftime('%Y%m%d')
    
    try:
        print(f"获取财务数据: {ts_code}, 时间范围: {start_date} - {end_date}")
        
        # 获取资产负债表数据
        balancesheet = pro.balancesheet(
            ts_code=ts_code,
            start_date=start_date,
            end_date=end_date,
            fields='ts_code,end_date,total_assets,total_liab,total_hldr_eqy_exc_min_int,report_type'
        )
        
        # 获取利润表数据
        income = pro.income(
            ts_code=ts_code,
            start_date=start_date,
            end_date=end_date,
            fields='ts_code,end_date,n_income,n_income_attr_p,revenue,report_type'
        )
        
        # 获取现金流量表数据
        cashflow = pro.cashflow(
            ts_code=ts_code,
            start_date=start_date,
            end_date=end_date,
            fields='ts_code,end_date,n_cashflow_act,n_cashflow_inv_act,n_cashflow_fin_act,report_type'
        )
        
        return balancesheet, income, cashflow
    except Exception as e:
        print(f"获取财务数据失败: {e}")
        return None, None, None

def get_daily_basic_range(ts_code, start_date=None, end_date=None):
    """获取指定时间范围的每日基础数据（市值等）"""
    if end_date is None:
        end_date = datetime.now().strftime('%Y%m%d')
    if start_date is None:
        start_date = (datetime.now() - timedelta(days=30)).strftime('%Y%m%d')
    
    try:
        print(f"获取每日基础数据: {ts_code}, 时间范围: {start_date} - {end_date}")
        
        daily_basic = pro.daily_basic(
            ts_code=ts_code,
            start_date=start_date,
            end_date=end_date,
            fields='ts_code,trade_date,circ_mv,total_mv,pe_ttm,pb,ps_ttm,dv_ttm,total_share,float_share'
        )
        return daily_basic
    except Exception as e:
        print(f"获取每日基础数据失败: {e}")
        return None

def get_moneyflow_data_range(ts_code, start_date=None, end_date=None):
    """获取指定时间范围的资金流向数据"""
    if end_date is None:
        end_date = datetime.now().strftime('%Y%m%d')
    if start_date is None:
        start_date = (datetime.now() - timedelta(days=30)).strftime('%Y%m%d')
    
    try:
        print(f"获取资金流向数据: {ts_code}, 时间范围: {start_date} - {end_date}")
        
        # 个股资金流向
        moneyflow = pro.moneyflow(
            ts_code=ts_code,
            start_date=start_date,
            end_date=end_date,
            fields='ts_code,trade_date,buy_sm_amount,sell_sm_amount,buy_md_amount,sell_md_amount,buy_lg_amount,sell_lg_amount,buy_elg_amount,sell_elg_amount'
        )
        return moneyflow
    except Exception as e:
        print(f"获取资金流向数据失败: {e}")
        return None

def get_stock_price_range(ts_code, start_date=None, end_date=None):
    """获取指定时间范围的股价数据"""
    if end_date is None:
        end_date = datetime.now().strftime('%Y%m%d')
    if start_date is None:
        start_date = (datetime.now() - timedelta(days=30)).strftime('%Y%m%d')
    
    try:
        print(f"获取股价数据: {ts_code}, 时间范围: {start_date} - {end_date}")
        
        # 获取日线行情
        price_data = pro.daily(
            ts_code=ts_code,
            start_date=start_date,
            end_date=end_date,
            fields='ts_code,trade_date,open,high,low,close,pre_close,change,pct_chg,vol,amount'
        )
        return price_data
    except Exception as e:
        print(f"获取股价数据失败: {e}")
        return None

def get_index_member_info(ts_code):
    """获取指数成分股信息"""
    index_codes = {
        '000300.SH': '沪深300',
        '000016.SH': '上证50',
        '000905.SH': '中证500',
        '000852.SH': '中证1000',
        '932000.CSI': '中证2000',
        '399006.SZ': '创业板指'
    }
    
    member_info = {}
    
    for index_code, index_name in index_codes.items():
        try:
            members = pro.index_weight(
                index_code=index_code,
                trade_date='',  # 最新数据
                fields='index_code,con_code'
            )
            if not members.empty and ts_code in members['con_code'].values:
                member_info[index_name] = 1
            else:
                member_info[index_name] = 0
            time.sleep(0.1)  # 避免频率限制
        except Exception as e:
            print(f"获取{index_name}成分股信息失败: {e}")
            member_info[index_name] = 0
    
    return member_info

def get_industry_info(ts_code):
    """获取申万行业分类信息"""
    try:
        # 获取申万行业分类
        stock_industry = pro.stock_industry(
            ts_code=ts_code,
            fields='ts_code,industry_name,industry_code,level'
        )
        
        industry_info = {
            '申万一级行业': '',
            '申万二级行业': '',
            '申万三级行业': ''
        }
        
        if not stock_industry.empty:
            for _, row in stock_industry.iterrows():
                if row['level'] == 'L1':
                    industry_info['申万一级行业'] = row['industry_name']
                elif row['level'] == 'L2':
                    industry_info['申万二级行业'] = row['industry_name']
                elif row['level'] == 'L3':
                    industry_info['申万三级行业'] = row['industry_name']
        
        return industry_info
    except Exception as e:
        print(f"获取行业信息失败: {e}")
        return {'申万一级行业': '', '申万二级行业': '', '申万三级行业': ''}

def aggregate_financial_data(balancesheet, income, cashflow, daily_basic, moneyflow, price_data):
    """聚合时间范围内的财务数据"""
    result = {}
    
    # 处理资产负债表数据 - 取最新的数据
    if balancesheet is not None and not balancesheet.empty:
        latest_balance = balancesheet.iloc[0]  # 已按时间排序，第一条是最新的
        result.update({
            '净资产': latest_balance.get('total_hldr_eqy_exc_min_int'),
            '总资产': latest_balance.get('total_assets'),
            '总负债': latest_balance.get('total_liab'),
            '资产负债表日期': latest_balance.get('end_date')
        })
    
    # 处理利润表数据 - 计算TTM（最近4个季度）
    if income is not None and not income.empty:
        # 取最新4个季度数据计算TTM
        recent_income = income.head(4)
        ttm_net_profit = recent_income['n_income_attr_p'].sum() if 'n_income_attr_p' in recent_income.columns else None
        ttm_revenue = recent_income['revenue'].sum() if 'revenue' in recent_income.columns else None
        
        result.update({
            '净利润TTM': ttm_net_profit,
            '营收TTM': ttm_revenue,
            '最新季度净利润': income.iloc[0].get('n_income_attr_p'),
            '最新季度营收': income.iloc[0].get('revenue'),
            '利润表日期': income.iloc[0].get('end_date')
        })
    
    # 处理现金流数据 - 计算TTM
    if cashflow is not None and not cashflow.empty:
        recent_cashflow = cashflow.head(4)
        ttm_operating_cashflow = recent_cashflow['n_cashflow_act'].sum() if 'n_cashflow_act' in recent_cashflow.columns else None
        
        result.update({
            '经营现金流TTM': ttm_operating_cashflow,
            '最新季度经营现金流': cashflow.iloc[0].get('n_cashflow_act'),
            '现金流表日期': cashflow.iloc[0].get('end_date')
        })
    
    # 处理每日基础数据 - 取最新的市值和估值数据
    if daily_basic is not None and not daily_basic.empty:
        latest_daily = daily_basic.iloc[0]
        result.update({
            '流通市值': latest_daily.get('circ_mv'),
            '总市值': latest_daily.get('total_mv'),
            'PE_TTM': latest_daily.get('pe_ttm'),
            'PB': latest_daily.get('pb'),
            'PS_TTM': latest_daily.get('ps_ttm'),
            '股息率TTM': latest_daily.get('dv_ttm'),
            '总股本': latest_daily.get('total_share'),
            '流通股本': latest_daily.get('float_share'),
            '市值数据日期': latest_daily.get('trade_date')
        })
    
    # 处理资金流向数据 - 计算时间范围内的累计值
    if moneyflow is not None and not moneyflow.empty:
        # 计算时间范围内的资金流向汇总
        total_buy_sm = moneyflow['buy_sm_amount'].sum() if 'buy_sm_amount' in moneyflow.columns else 0
        total_sell_sm = moneyflow['sell_sm_amount'].sum() if 'sell_sm_amount' in moneyflow.columns else 0
        total_buy_md = moneyflow['buy_md_amount'].sum() if 'buy_md_amount' in moneyflow.columns else 0
        total_sell_md = moneyflow['sell_md_amount'].sum() if 'sell_md_amount' in moneyflow.columns else 0
        total_buy_lg = moneyflow['buy_lg_amount'].sum() if 'buy_lg_amount' in moneyflow.columns else 0
        total_sell_lg = moneyflow['sell_lg_amount'].sum() if 'sell_lg_amount' in moneyflow.columns else 0
        total_buy_elg = moneyflow['buy_elg_amount'].sum() if 'buy_elg_amount' in moneyflow.columns else 0
        total_sell_elg = moneyflow['sell_elg_amount'].sum() if 'sell_elg_amount' in moneyflow.columns else 0
        
        result.update({
            '散户资金买入额_累计': total_buy_sm,
            '散户资金卖出额_累计': total_sell_sm,
            '散户资金净流入_累计': total_buy_sm - total_sell_sm,
            '中户资金买入额_累计': total_buy_md,
            '中户资金卖出额_累计': total_sell_md,
            '中户资金净流入_累计': total_buy_md - total_sell_md,
            '大户资金买入额_累计': total_buy_lg,
            '大户资金卖出额_累计': total_sell_lg,
            '大户资金净流入_累计': total_buy_lg - total_sell_lg,
            '机构资金买入额_累计': total_buy_elg,
            '机构资金卖出额_累计': total_sell_elg,
            '机构资金净流入_累计': total_buy_elg - total_sell_elg,
            '资金流向统计开始日期': moneyflow.iloc[-1].get('trade_date') if len(moneyflow) > 0 else None,
            '资金流向统计结束日期': moneyflow.iloc[0].get('trade_date') if len(moneyflow) > 0 else None
        })
    
    # 处理价格数据 - 计算收益率等指标
    if price_data is not None and not price_data.empty:
        # 计算时间范围内的价格变化
        start_price = price_data.iloc[-1]['close'] if len(price_data) > 0 else None  # 最早的收盘价
        end_price = price_data.iloc[0]['close'] if len(price_data) > 0 else None    # 最新的收盘价
        
        period_return = ((end_price - start_price) / start_price * 100) if start_price and end_price else None
        
        # 计算波动率（标准差）
        if 'pct_chg' in price_data.columns and len(price_data) > 1:
            volatility = price_data['pct_chg'].std()
        else:
            volatility = None
        
        # 计算平均成交量和成交额
        avg_volume = price_data['vol'].mean() if 'vol' in price_data.columns else None
        avg_amount = price_data['amount'].mean() if 'amount' in price_data.columns else None
        
        result.update({
            '期间收益率(%)': period_return,
            '期间开始价格': start_price,
            '期间结束价格': end_price,
            '期间最高价': price_data['high'].max() if 'high' in price_data.columns else None,
            '期间最低价': price_data['low'].min() if 'low' in price_data.columns else None,
            '日收益率波动率(%)': volatility,
            '平均日成交量': avg_volume,
            '平均日成交额': avg_amount,
            '价格统计开始日期': price_data.iloc[-1].get('trade_date') if len(price_data) > 0 else None,
            '价格统计结束日期': price_data.iloc[0].get('trade_date') if len(price_data) > 0 else None
        })
    
    return result

def get_comprehensive_stock_data_range(ts_code, start_date=None, end_date=None, 
                                     financial_start_date=None, financial_end_date=None):
    """获取指定时间范围内单只股票的综合数据
    
    Args:
        ts_code: 股票代码
        start_date: 行情数据开始日期 (YYYYMMDD)
        end_date: 行情数据结束日期 (YYYYMMDD) 
        financial_start_date: 财务数据开始日期 (YYYYMMDD)，如果为None则使用start_date
        financial_end_date: 财务数据结束日期 (YYYYMMDD)，如果为None则使用end_date
    """
    print(f"正在获取股票 {ts_code} 的时间范围数据...")
    
    # 如果没有指定财务数据的时间范围，则使用行情数据的时间范围
    if financial_start_date is None:
        financial_start_date = start_date
    if financial_end_date is None:
        financial_end_date = end_date
    
    # 1. 获取基础信息
    try:
        stock_info = pro.stock_basic(ts_code=ts_code)
    except Exception as e:
        print(f"获取基础信息失败: {e}")
        stock_info = pd.DataFrame()
    
    # 2. 获取时间范围内的各类数据
    daily_basic = get_daily_basic_range(ts_code, start_date, end_date)
    balancesheet, income, cashflow = get_financial_data_range(ts_code, financial_start_date, financial_end_date)
    moneyflow = get_moneyflow_data_range(ts_code, start_date, end_date)
    price_data = get_stock_price_range(ts_code, start_date, end_date)
    
    # 3. 获取指数成分股信息（不受时间限制）
    index_member = get_index_member_info(ts_code)
    
    # 4. 获取行业信息（不受时间限制）
    industry_info = get_industry_info(ts_code)
    
    # 5. 聚合时间范围内的数据
    aggregated_data = aggregate_financial_data(balancesheet, income, cashflow, 
                                             daily_basic, moneyflow, price_data)
    
    # 6. 整合所有数据
    result = {
        'ts_code': ts_code,
        'name': stock_info.iloc[0]['name'] if not stock_info.empty else '',
        'symbol': stock_info.iloc[0]['symbol'] if not stock_info.empty else '',
        'area': stock_info.iloc[0]['area'] if not stock_info.empty else '',
        'industry': stock_info.iloc[0]['industry'] if not stock_info.empty else '',
        'market': stock_info.iloc[0]['market'] if not stock_info.empty else '',
        'list_date': stock_info.iloc[0]['list_date'] if not stock_info.empty else '',
        
        # 查询时间范围
        '查询开始日期': start_date,
        '查询结束日期': end_date,
        '财务数据开始日期': financial_start_date,
        '财务数据结束日期': financial_end_date,
    }
    
    # 添加聚合后的财务数据
    result.update(aggregated_data)
    
    # 添加指数成分股信息
    result.update({
        '沪深300成分股': index_member.get('沪深300', 0),
        '上证50成分股': index_member.get('上证50', 0),
        '中证500成分股': index_member.get('中证500', 0),
        '中证1000成分股': index_member.get('中证1000', 0),
        '中证2000成分股': index_member.get('中证2000', 0),
        '创业板指成分股': index_member.get('创业板指', 0),
    })
    
    # 添加行业信息
    result.update({
        '新版申万一级行业名称': industry_info.get('申万一级行业', ''),
        '新版申万二级行业名称': industry_info.get('申万二级行业', ''),
        '新版申万三级行业名称': industry_info.get('申万三级行业', ''),
    })
    
    return result

def test_stock_with_date_range():
    """测试指定时间范围的股票数据获取"""
    test_code = '000001.SZ'  # 平安银行
    
    # 设置时间范围
    end_date = datetime.now().strftime('%Y%m%d')
    start_date = (datetime.now() - timedelta(days=90)).strftime('%Y%m%d')  # 最近3个月
    financial_start_date = (datetime.now() - timedelta(days=365)).strftime('%Y%m%d')  # 最近1年财务数据
    
    print("=" * 50)
    print(f"测试时间范围股票数据获取")
    print(f"股票代码: {test_code}")
    print(f"行情数据时间范围: {start_date} - {end_date}")
    print(f"财务数据时间范围: {financial_start_date} - {end_date}")
    print("=" * 50)
    
    result = get_comprehensive_stock_data_range(
        ts_code=test_code, 
        start_date=start_date, 
        end_date=end_date,
        financial_start_date=financial_start_date,
        financial_end_date=end_date
    )
    
    # 打印结果
    print(f"\n股票代码: {result['ts_code']}")
    print(f"股票名称: {result['name']}")
    print(f"所属地区: {result['area']}")
    print(f"所属行业: {result['industry']}")
    print(f"上市日期: {result['list_date']}")
    
    print(f"\n查询时间范围:")
    print(f"行情数据: {result['查询开始日期']} - {result['查询结束日期']}")
    print(f"财务数据: {result['财务数据开始日期']} - {result['财务数据结束日期']}")
    
    print(f"\n最新市值数据 (日期: {result.get('市值数据日期', 'N/A')}):")
    print(f"流通市值: {result.get('流通市值', 'N/A')} 万元")
    print(f"总市值: {result.get('总市值', 'N/A')} 万元")
    print(f"PE_TTM: {result.get('PE_TTM', 'N/A')}")
    print(f"PB: {result.get('PB', 'N/A')}")
    
    print(f"\n财务数据 (TTM):")
    print(f"净利润TTM: {result.get('净利润TTM', 'N/A')} 万元")
    print(f"营收TTM: {result.get('营收TTM', 'N/A')} 万元")
    print(f"经营现金流TTM: {result.get('经营现金流TTM', 'N/A')} 万元")
    print(f"净资产: {result.get('净资产', 'N/A')} 万元")
    
    print(f"\n期间价格表现:")
    print(f"期间收益率: {result.get('期间收益率(%)', 'N/A')}%")
    print(f"期间最高价: {result.get('期间最高价', 'N/A')}")
    print(f"期间最低价: {result.get('期间最低价', 'N/A')}")
    print(f"日收益率波动率: {result.get('日收益率波动率(%)', 'N/A')}%")
    
    print(f"\n期间资金流向累计:")
    print(f"散户资金净流入: {result.get('散户资金净流入_累计', 'N/A')}")
    print(f"中户资金净流入: {result.get('中户资金净流入_累计', 'N/A')}")
    print(f"大户资金净流入: {result.get('大户资金净流入_累计', 'N/A')}")
    print(f"机构资金净流入: {result.get('机构资金净流入_累计', 'N/A')}")
    
    print(f"\n指数成分股:")
    print(f"沪深300: {'是' if result['沪深300成分股'] else '否'}")
    print(f"上证50: {'是' if result['上证50成分股'] else '否'}")
    print(f"中证500: {'是' if result['中证500成分股'] else '否'}")
    
    print(f"\n行业分类:")
    print(f"申万一级行业: {result['新版申万一级行业名称']}")
    print(f"申万二级行业: {result['新版申万二级行业名称']}")
    
    return result

def get_custom_date_range():
    """获取用户自定义的时间范围"""
    print("\n请设置时间范围:")
    
    # 获取结束日期
    end_input = input("请输入结束日期 (格式: YYYY-MM-DD，回车使用今天): ").strip()
    if not end_input:
        end_date = datetime.now().strftime('%Y%m%d')
        print(f"使用今天作为结束日期: {datetime.now().strftime('%Y-%m-%d')}")
    else:
        try:
            end_date = datetime.strptime(end_input, '%Y-%m-%d').strftime('%Y%m%d')
        except:
            print("日期格式错误，使用今天作为结束日期")
            end_date = datetime.now().strftime('%Y%m%d')
    
    # 获取开始日期
    start_input = input("请输入开始日期 (格式: YYYY-MM-DD，回车使用30天前): ").strip()
    if not start_input:
        start_date = (datetime.now() - timedelta(days=30)).strftime('%Y%m%d')
        print(f"使用30天前作为开始日期: {(datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')}")
    else:
        try:
            start_date = datetime.strptime(start_input, '%Y-%m-%d').strftime('%Y%m%d')
        except:
            print("日期格式错误，使用30天前作为开始日期")
            start_date = (datetime.now() - timedelta(days=30)).strftime('%Y%m%d')
    
    # 询问是否单独设置财务数据时间范围
    use_separate = input("是否单独设置财务数据时间范围? (y/n，回车选择n): ").strip().lower()
    if use_separate == 'y':
        financial_end_input = input("财务数据结束日期 (格式: YYYY-MM-DD，回车使用行情结束日期): ").strip()
        if not financial_end_input:
            financial_end_date = end_date
        else:
            try:
                financial_end_date = datetime.strptime(financial_end_input, '%Y-%m-%d').strftime('%Y%m%d')
            except:
                print("日期格式错误，使用行情结束日期")
                financial_end_date = end_date
        
        financial_start_input = input("财务数据开始日期 (格式: YYYY-MM-DD，回车使用1年前): ").strip()
        if not financial_start_input:
            financial_start_date = (datetime.strptime(financial_end_date, '%Y%m%d') - timedelta(days=365)).strftime('%Y%m%d')
        else:
            try:
                financial_start_date = datetime.strptime(financial_start_input, '%Y-%m-%d').strftime('%Y%m%d')
            except:
                print("日期格式错误，使用1年前")
                financial_start_date = (datetime.strptime(financial_end_date, '%Y%m%d') - timedelta(days=365)).strftime('%Y%m%d')
    else:
        financial_start_date = None
        financial_end_date = None
    
    return start_date, end_date, financial_start_date, financial_end_date

def get_batch_stocks_data_range(max_stocks=None, start_date=None, end_date=None, 
                               financial_start_date=None, financial_end_date=None):
    """获取批量股票的时间范围数据"""
    print("=" * 50)
    print("开始获取批量股票时间范围数据")
    print("=" * 50)
    
    # 获取股票列表
    stock_list = get_stock_basic_info()
    if stock_list is None or stock_list.empty:
        print("获取股票列表失败")
        return None
    
    print(f"共找到 {len(stock_list)} 只股票")
    
    if max_stocks:
        stock_list = stock_list.head(max_stocks)
        print(f"限制获取前 {max_stocks} 只股票")
    
    # 显示时间范围信息
    print(f"行情数据时间范围: {start_date} - {end_date}")
    if financial_start_date and financial_end_date:
        print(f"财务数据时间范围: {financial_start_date} - {financial_end_date}")
    else:
        print("财务数据使用与行情数据相同的时间范围")
    
    all_data = []
    total_stocks = len(stock_list)
    
    for idx, (_, stock) in enumerate(stock_list.iterrows()):
        ts_code = stock['ts_code']
        print(f"进度: {idx+1}/{total_stocks} - 正在处理 {ts_code}")
        
        try:
            stock_data = get_comprehensive_stock_data_range(
                ts_code=ts_code,
                start_date=start_date,
                end_date=end_date,
                financial_start_date=financial_start_date,
                financial_end_date=financial_end_date
            )
            all_data.append(stock_data)
            
            # 避免频率限制，每次请求间隔
            time.sleep(0.3)
            
        except Exception as e:
            print(f"处理股票 {ts_code} 时出错: {e}")
            continue
        
        # 每处理10只股票显示一次进度
        if (idx + 1) % 10 == 0:
            print(f"已完成 {idx + 1}/{total_stocks} 只股票的数据获取")
    
    # 转换为DataFrame
    if all_data:
        df = pd.DataFrame(all_data)
        print(f"\n成功获取 {len(df)} 只股票的数据")
        return df
    else:
        print("没有获取到任何股票数据")
        return None

def save_to_excel_with_sheets(df, filename=None, include_summary=True):
    """保存数据到Excel，包含多个工作表"""
    if filename is None:
        filename = f'股票时间范围数据_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
    
    try:
        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            # 主数据表
            df.to_excel(writer, sheet_name='股票数据', index=False)
            
            if include_summary and len(df) > 0:
                # 创建汇总统计表
                summary_data = []
                
                # 基础统计
                summary_data.append(['数据概览', ''])
                summary_data.append(['股票总数', len(df)])
                summary_data.append(['数据获取时间', datetime.now().strftime("%Y-%m-%d %H:%M:%S")])
                summary_data.append(['查询开始日期', df['查询开始日期'].iloc[0] if '查询开始日期' in df.columns else 'N/A'])
                summary_data.append(['查询结束日期', df['查询结束日期'].iloc[0] if '查询结束日期' in df.columns else 'N/A'])
                summary_data.append(['', ''])
                
                # 市值统计
                if '总市值' in df.columns:
                    market_values = df['总市值'].dropna()
                    if len(market_values) > 0:
                        summary_data.append(['市值统计 (万元)', ''])
                        summary_data.append(['平均总市值', market_values.mean()])
                        summary_data.append(['中位数总市值', market_values.median()])
                        summary_data.append(['最大总市值', market_values.max()])
                        summary_data.append(['最小总市值', market_values.min()])
                        summary_data.append(['', ''])
                
                # 收益率统计
                if '期间收益率(%)' in df.columns:
                    returns = df['期间收益率(%)'].dropna()
                    if len(returns) > 0:
                        summary_data.append(['期间收益率统计 (%)', ''])
                        summary_data.append(['平均收益率', returns.mean()])
                        summary_data.append(['中位数收益率', returns.median()])
                        summary_data.append(['最大收益率', returns.max()])
                        summary_data.append(['最小收益率', returns.min()])
                        summary_data.append(['正收益股票数量', (returns > 0).sum()])
                        summary_data.append(['负收益股票数量', (returns < 0).sum()])
                        summary_data.append(['', ''])
                
                # 行业分布
                if '新版申万一级行业名称' in df.columns:
                    industry_counts = df['新版申万一级行业名称'].value_counts()
                    summary_data.append(['申万一级行业分布', ''])
                    for industry, count in industry_counts.head(10).items():
                        if industry and str(industry) != 'nan':
                            summary_data.append([industry, count])
                    summary_data.append(['', ''])
                
                # 指数成分股统计
                index_cols = ['沪深300成分股', '上证50成分股', '中证500成分股', '中证1000成分股', '中证2000成分股', '创业板指成分股']
                summary_data.append(['指数成分股统计', ''])
                for col in index_cols:
                    if col in df.columns:
                        count = df[col].sum()
                        summary_data.append([col.replace('成分股', ''), count])
                
                # 创建汇总表
                summary_df = pd.DataFrame(summary_data, columns=['项目', '数值'])
                summary_df.to_excel(writer, sheet_name='数据汇总', index=False)
                
                # 如果有收益率数据，创建收益率分布表
                if '期间收益率(%)' in df.columns:
                    returns_data = df[['ts_code', 'name', '期间收益率(%)', '期间最高价', '期间最低价', 
                                     '日收益率波动率(%)', '总市值']].copy()
                    returns_data = returns_data.dropna(subset=['期间收益率(%)'])
                    returns_data = returns_data.sort_values('期间收益率(%)', ascending=False)
                    returns_data.to_excel(writer, sheet_name='收益率排行', index=False)
        
        print(f"数据已保存到: {filename}")
        return filename
    except Exception as e:
        print(f"保存Excel文件失败: {e}")
        return None

def main():
    """主函数 - 增强版，支持时间范围"""
    print("=" * 60)
    print("股票数据获取工具 - 增强版 (支持时间范围)")
    print("=" * 60)
    print("请确保已经安装以下包:")
    print("pip install tushare pandas openpyxl")
    print("=" * 60)
    
    # 检查token是否设置
    if TOKEN == 'your_tushare_token_here':
        print("请先设置你的tushare token!")
        print("注册地址: https://tushare.pro/register")
        print("获取token后，请在代码开头的 TOKEN 变量中替换你的token")
        return
    
    print("选择执行模式:")
    print("1. 测试单只股票 - 使用默认时间范围")
    print("2. 测试单只股票 - 自定义时间范围") 
    print("3. 获取前10只股票数据 - 自定义时间范围")
    print("4. 获取指定数量股票数据 - 自定义时间范围")
    print("5. 获取全部股票数据 - 自定义时间范围")
    
    choice = input("\n请输入选择 (1/2/3/4/5): ").strip()
    
    if choice == '1':
        # 测试单只股票 - 默认时间范围
        print("\n使用默认时间范围进行测试:")
        print("- 行情数据: 最近30天")
        print("- 财务数据: 最近1年")
        result = test_stock_with_date_range()
        print("\n测试完成!")
        
    elif choice == '2':
        # 测试单只股票 - 自定义时间范围
        start_date, end_date, financial_start_date, financial_end_date = get_custom_date_range()
        
        # 获取股票代码
        stock_code = input("\n请输入股票代码 (默认: 000001.SZ): ").strip()
        if not stock_code:
            stock_code = '000001.SZ'
        
        print(f"\n开始获取 {stock_code} 的数据...")
        result = get_comprehensive_stock_data_range(
            ts_code=stock_code,
            start_date=start_date,
            end_date=end_date,
            financial_start_date=financial_start_date,
            financial_end_date=financial_end_date
        )
        
        # 显示关键信息
        print(f"\n=== {result.get('name', stock_code)} 数据获取完成 ===")
        print(f"期间收益率: {result.get('期间收益率(%)', 'N/A')}%")
        print(f"总市值: {result.get('总市值', 'N/A')} 万元")
        print(f"净利润TTM: {result.get('净利润TTM', 'N/A')} 万元")
        
    elif choice in ['3', '4', '5']:
        # 批量获取股票数据
        start_date, end_date, financial_start_date, financial_end_date = get_custom_date_range()
        
        if choice == '3':
            max_stocks = 10
        elif choice == '4':
            max_input = input("\n请输入要获取的股票数量: ").strip()
            try:
                max_stocks = int(max_input)
            except:
                print("输入无效，使用默认值50")
                max_stocks = 50
        else:  # choice == '5'
            print("\n警告: 获取全部股票数据需要非常长的时间（可能几小时）")
            print("建议先测试少量数据确认程序正常运行")
            confirm = input("确认继续获取全部股票数据? (y/n): ").strip().lower()
            if confirm != 'y':
                print("已取消")
                return
            max_stocks = None
        
        print(f"\n开始批量获取股票数据...")
        print(f"股票数量限制: {'全部' if max_stocks is None else max_stocks}")
        
        df = get_batch_stocks_data_range(
            max_stocks=max_stocks,
            start_date=start_date,
            end_date=end_date,
            financial_start_date=financial_start_date,
            financial_end_date=financial_end_date
        )
        
        if df is not None and len(df) > 0:
            print(f"\n成功获取 {len(df)} 只股票数据")
            
            # 显示一些汇总统计
            if '期间收益率(%)' in df.columns:
                returns = df['期间收益率(%)'].dropna()
                if len(returns) > 0:
                    print(f"\n期间收益率统计:")
                    print(f"平均收益率: {returns.mean():.2f}%")
                    print(f"中位数收益率: {returns.median():.2f}%")
                    print(f"最大收益率: {returns.max():.2f}%")
                    print(f"最小收益率: {returns.min():.2f}%")
                    print(f"正收益股票: {(returns > 0).sum()} 只")
                    print(f"负收益股票: {(returns < 0).sum()} 只")
            
            # 询问是否保存
            save_choice = input("\n是否保存到Excel? (y/n): ").strip().lower()
            if save_choice == 'y':
                filename = save_to_excel_with_sheets(df, include_summary=True)
                if filename:
                    print(f"数据已保存，包含以下工作表:")
                    print("- 股票数据: 完整的股票数据")
                    print("- 数据汇总: 统计信息汇总") 
                    print("- 收益率排行: 按收益率排序的股票列表")
        else:
            print("未获取到任何数据")
    
    else:
        print("无效选择")

# 新增数据字段说明
ENHANCED_FIELD_DESCRIPTIONS = {
    # 基础信息
    'ts_code': '股票代码',
    'name': '股票名称', 
    'symbol': '股票代码(不含后缀)',
    'area': '所属地区',
    'industry': '所属行业',
    'market': '市场类型',
    'list_date': '上市日期',
    
    # 时间范围
    '查询开始日期': '行情数据查询开始日期',
    '查询结束日期': '行情数据查询结束日期', 
    '财务数据开始日期': '财务数据查询开始日期',
    '财务数据结束日期': '财务数据查询结束日期',
    
    # 最新市值和估值数据
    '流通市值': '流通股本*当日收盘价（万元）',
    '总市值': '总股本*当日收盘价（万元）',
    'PE_TTM': '市盈率TTM',
    'PB': '市净率',
    'PS_TTM': '市销率TTM', 
    '股息率TTM': '股息率TTM(%)',
    '总股本': '总股本（万股）',
    '流通股本': '流通股本（万股）',
    '市值数据日期': '市值数据对应日期',
    
    # 财务数据TTM
    '净利润TTM': '归属母公司股东的净利润TTM（万元）',
    '营收TTM': '营业收入TTM（万元）',
    '经营现金流TTM': '经营活动产生的现金流量净额TTM（万元）',
    '最新季度净利润': '最新季度归属母公司净利润（万元）',
    '最新季度营收': '最新季度营业收入（万元）',
    '最新季度经营现金流': '最新季度经营现金流（万元）',
    
    # 最新资产负债数据  
    '净资产': '归属母公司所有者权益合计（万元）',
    '总资产': '资产总计（万元）',
    '总负债': '负债合计（万元）',
    '资产负债表日期': '资产负债表数据日期',
    '利润表日期': '利润表数据日期',
    '现金流表日期': '现金流表数据日期',
    
    # 期间价格表现
    '期间收益率(%)': '查询时间范围内的收益率',
    '期间开始价格': '查询开始日期的收盘价',
    '期间结束价格': '查询结束日期的收盘价',
    '期间最高价': '查询期间最高价',
    '期间最低价': '查询期间最低价', 
    '日收益率波动率(%)': '日收益率标准差',
    '平均日成交量': '查询期间平均日成交量',
    '平均日成交额': '查询期间平均日成交额',
    '价格统计开始日期': '价格统计开始日期',
    '价格统计结束日期': '价格统计结束日期',
    
    # 期间资金流向累计
    '散户资金买入额_累计': '查询期间散户资金买入累计金额',
    '散户资金卖出额_累计': '查询期间散户资金卖出累计金额',
    '散户资金净流入_累计': '查询期间散户资金净流入累计金额',
    '中户资金买入额_累计': '查询期间中户资金买入累计金额',
    '中户资金卖出额_累计': '查询期间中户资金卖出累计金额',
    '中户资金净流入_累计': '查询期间中户资金净流入累计金额',
    '大户资金买入额_累计': '查询期间大户资金买入累计金额',
    '大户资金卖出额_累计': '查询期间大户资金卖出累计金额',
    '大户资金净流入_累计': '查询期间大户资金净流入累计金额',
    '机构资金买入额_累计': '查询期间机构资金买入累计金额',
    '机构资金卖出额_累计': '查询期间机构资金卖出累计金额',
    '机构资金净流入_累计': '查询期间机构资金净流入累计金额',
    '资金流向统计开始日期': '资金流向统计开始日期',
    '资金流向统计结束日期': '资金流向统计结束日期',
    
    # 指数成分股
    '沪深300成分股': '是否为沪深300成分股 (1是/0否)',
    '上证50成分股': '是否为上证50成分股 (1是/0否)',
    '中证500成分股': '是否为中证500成分股 (1是/0否)',
    '中证1000成分股': '是否为中证1000成分股 (1是/0否)',
    '中证2000成分股': '是否为中证2000成分股 (1是/0否)',
    '创业板指成分股': '是否为创业板指成分股 (1是/0否)',
    
    # 行业分类
    '新版申万一级行业名称': '申万一级行业分类',
    '新版申万二级行业名称': '申万二级行业分类', 
    '新版申万三级行业名称': '申万三级行业分类'
}

def print_field_descriptions():
    """打印字段说明"""
    print("=" * 60)
    print("数据字段说明")
    print("=" * 60)
    
    categories = {
        '基础信息': ['ts_code', 'name', 'symbol', 'area', 'industry', 'market', 'list_date'],
        '时间范围': ['查询开始日期', '查询结束日期', '财务数据开始日期', '财务数据结束日期'],
        '市值估值': ['流通市值', '总市值', 'PE_TTM', 'PB', 'PS_TTM', '股息率TTM', '总股本', '流通股本'],
        '财务数据': ['净利润TTM', '营收TTM', '经营现金流TTM', '净资产', '总资产', '总负债'],
        '价格表现': ['期间收益率(%)', '期间最高价', '期间最低价', '日收益率波动率(%)'],
        '资金流向': ['散户资金净流入_累计', '中户资金净流入_累计', '大户资金净流入_累计', '机构资金净流入_累计'],
        '指数成分': ['沪深300成分股', '上证50成分股', '中证500成分股', '中证1000成分股'],
        '行业分类': ['新版申万一级行业名称', '新版申万二级行业名称', '新版申万三级行业名称']
    }
    
    for category, fields in categories.items():
        print(f"\n{category}:")
        print("-" * 40)
        for field in fields:
            if field in ENHANCED_FIELD_DESCRIPTIONS:
                print(f"{field}: {ENHANCED_FIELD_DESCRIPTIONS[field]}")

if __name__ == "__main__":
    # 询问是否显示字段说明
    show_desc = input("是否显示数据字段说明? (y/n，回车选择n): ").strip().lower()
    if show_desc == 'y':
        print_field_descriptions()
        input("\n按回车键继续...")
    
    main()