
import datetime
from xtquant import xtdata
import tushare as ts
token='2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211'
pro=ts.pro_api(token)
import os
from joblib import Parallel, delayed
import warnings
warnings.filterwarnings("ignore")

# dividend_type: 复权类型, none, front, back, front_ratio, back_ratio
dividend_type = 'front'
# period: 数据周期, 1m, 5m, 15m, 30m, 1h, 1d, tick
period = '5m'

def download_data(stock, period):
    try:
        xtdata.download_history_data(stock_code = stock, period = period)
    except:
        print(stock, 'data failed')

# 1 获取股票列表
print('获取股票列表...')
df = pro.daily_basic(ts_code='', trade_date='20241108')
stock_list = df['ts_code'].tolist()
print('获取股票列表完成')

# 2 下载数据
print('下载数据...')
res = Parallel(n_jobs=4)(delayed(download_data)(stock, period) for stock in stock_list)   
print('下载数据完成') 

# 3 整理数据，将下载的加密数据整理成DataFrame保存成本地.csv文件
print('整理数据...')
folder_path = 'data/' + period + '/' + dividend_type
if not os.path.exists(folder_path): os.makedirs(folder_path)
for i, stock in enumerate(stock_list):
    # 隔50个打印下进度
    if i%50 == 0: print(i,stock)
    data = xtdata.get_local_data(field_list=['time', 'open','close','high','low','volume',
                                             'amount','settelementPrice', 'openInterest',
                                             'preClose', 'suspendFlag'],
                                 stock_list=[stock],
                                 period=period,
                                 dividend_type=dividend_type)
    df = data[stock]
    try:
        df['datetime'] = df['time'].apply(lambda x: datetime.datetime.fromtimestamp(x/1000.0))
        df.index = df['datetime']
        df = df[['open','close','high','low','volume','amount','preClose','suspendFlag']]
        df.loc[:,'code'] = stock
        df.to_csv(folder_path +'/' + stock + '.csv')
    except:
        pass
print('数据整理完成')
  
    
    
