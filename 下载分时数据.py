import datetime
from xtquant import xtdata
import tushare as ts
import pandas as pd
import os
from joblib import Parallel, delayed
import warnings
warnings.filterwarnings("ignore")

# tushare配置
token = '5154467945318d04b93f4e49e19fa24efcccedae10c8802936aadf47'
pro = ts.pro_api(token)

# dividend_type: 复权类型, none, front, back, front_ratio, back_ratio
dividend_type = 'front'
# period: 数据周期, 1m, 5m, 15m, 30m, 1h, 1d, tick
period = '1m'  # 1分钟数据

def download_minute_data(stock, period, trade_date=None):
    """下载指定股票的分钟数据"""
    try:
        if trade_date:
            # 下载指定日期的数据
            xtdata.download_history_data(
                stock_code=stock, 
                period=period,
                start_time=trade_date + ' 09:30:00',
                end_time=trade_date + ' 15:00:00'
            )
        else:
            # 下载所有历史数据
            xtdata.download_history_data(stock_code=stock, period=period)
    except Exception as e:
        print(f'{stock} 分钟数据下载失败: {e}')

def get_intraday_prices_from_local(stock, trade_date, target_times=['09:35', '09:40', '09:45']):
    """从本地数据提取指定时间点的价格"""
    try:
        # 获取本地1分钟数据
        data = xtdata.get_local_data(
            field_list=['time', 'open', 'close', 'high', 'low', 'volume', 'amount'],
            stock_list=[stock],
            period='1m',
            start_time=trade_date + ' 09:30:00',
            end_time=trade_date + ' 10:00:00'
        )
        
        if stock not in data or data[stock].empty:
            return {}
        
        df = data[stock]
        df['datetime'] = df['time'].apply(lambda x: datetime.datetime.fromtimestamp(x/1000.0))
        
        # 提取特定时间点的价格
        prices = {}
        for target_time in target_times:
            target_datetime = datetime.datetime.strptime(f"{trade_date} {target_time}:00", '%Y-%m-%d %H:%M:%S')
            
            # 找到最接近目标时间的数据点
            time_diff = abs(df['datetime'] - target_datetime)
            closest_idx = time_diff.idxmin()
            
            if time_diff[closest_idx] <= datetime.timedelta(minutes=2):  # 允许2分钟误差
                prices[f'{target_time}收盘价'] = df.loc[closest_idx, 'close']
                prices[f'{target_time}开盘价'] = df.loc[closest_idx, 'open']
                prices[f'{target_time}最高价'] = df.loc[closest_idx, 'high']
                prices[f'{target_time}最低价'] = df.loc[closest_idx, 'low']
                prices[f'{target_time}成交量'] = df.loc[closest_idx, 'volume']
                prices[f'{target_time}成交额'] = df.loc[closest_idx, 'amount']
            else:
                prices[f'{target_time}收盘价'] = None
                prices[f'{target_time}开盘价'] = None
                prices[f'{target_time}最高价'] = None
                prices[f'{target_time}最低价'] = None
                prices[f'{target_time}成交量'] = None
                prices[f'{target_time}成交额'] = None
        
        return prices
    
    except Exception as e:
        print(f'获取{stock}分时价格失败: {e}')
        return {}

def process_intraday_data(stock_list, trade_date, target_times=['09:35', '09:40', '09:45']):
    """处理分时数据，提取指定时间点的价格"""
    print('提取分时价格数据...')
    
    all_intraday_data = []
    
    for i, stock in enumerate(stock_list):
        # 隔50个打印下进度
        if i % 50 == 0: 
            print(f'{i}/{len(stock_list)} - {stock}')
        
        try:
            # 转换股票代码格式
            if stock.endswith('.SZ'):
                xt_stock = stock.replace('.SZ', '.SZ')
            elif stock.endswith('.SH'):
                xt_stock = stock.replace('.SH', '.SS')
            else:
                xt_stock = stock
            
            # 获取分时价格
            prices = get_intraday_prices_from_local(xt_stock, trade_date, target_times)
            
            if prices:
                row_data = {'股票代码': stock, '交易日期': trade_date}
                row_data.update(prices)
                all_intraday_data.append(row_data)
        
        except Exception as e:
            print(f'处理{stock}分时数据失败: {e}')
            continue
    
    if all_intraday_data:
        df = pd.DataFrame(all_intraday_data)
        return df
    else:
        return None

def main():
    """主函数"""
    print('=' * 60)
    print('分时数据下载和处理工具')
    print('=' * 60)
    
    # 获取交易日期
    date_input = input("请输入交易日期 (格式: YYYY-MM-DD，回车使用今天): ").strip()
    if not date_input:
        trade_date = datetime.datetime.now().strftime('%Y-%m-%d')
        print(f"使用今天: {trade_date}")
    else:
        try:
            datetime.datetime.strptime(date_input, '%Y-%m-%d')
            trade_date = date_input
        except:
            print("日期格式错误，使用今天")
            trade_date = datetime.datetime.now().strftime('%Y-%m-%d')
    
    print("选择操作:")
    print("1. 下载分钟数据")
    print("2. 提取分时价格")
    print("3. 下载并提取")
    
    choice = input("请选择 (1/2/3): ").strip()
    
    # 1 获取股票列表
    print('获取股票列表...')
    trade_date_ts = trade_date.replace('-', '')
    df = pro.daily_basic(ts_code='', trade_date=trade_date_ts)
    stock_list = df['ts_code'].tolist()
    print(f'获取股票列表完成，共{len(stock_list)}只股票')
    
    if choice in ['1', '3']:
        # 下载分钟数据
        print('开始下载分钟数据...')
        
        # 询问是否限制数量
        limit_input = input("是否限制下载数量? (输入数字限制，回车下载全部): ").strip()
        if limit_input.isdigit():
            stock_list = stock_list[:int(limit_input)]
            print(f"限制下载前{limit_input}只股票")
        
        # 并行下载
        res = Parallel(n_jobs=4)(delayed(download_minute_data)(stock, period, trade_date) for stock in stock_list)
        print('分钟数据下载完成')
    
    if choice in ['2', '3']:
        # 提取分时价格
        print('开始提取分时价格...')
        
        # 设置目标时间点
        time_input = input("请输入目标时间点 (格式: 09:35,09:40,09:45，回车使用默认): ").strip()
        if time_input:
            target_times = [t.strip() for t in time_input.split(',')]
        else:
            target_times = ['09:35', '09:40', '09:45']
        
        print(f"目标时间点: {target_times}")
        
        # 处理分时数据
        intraday_df = process_intraday_data(stock_list, trade_date, target_times)
        
        if intraday_df is not None:
            # 保存结果
            filename = f'分时价格数据_{trade_date.replace("-", "")}.xlsx'
            intraday_df.to_excel(filename, index=False)
            print(f'分时价格数据已保存到: {filename}')
            print(f'共提取{len(intraday_df)}只股票的分时数据')
            
            # 显示样例数据
            print('\n样例数据:')
            print(intraday_df.head())
        else:
            print('未提取到分时数据')

if __name__ == "__main__":
    main()
