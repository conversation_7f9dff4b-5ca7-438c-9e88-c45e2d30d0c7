# 股票数据获取工具使用说明

## 概述
基于您提供的两个参考代码，我创建了一个综合的股票数据获取工具，可以获取您需要的所有字段。

## 文件说明

### 1. `获取股票数据_简化版.py` (推荐使用)
- **功能**: 获取所有字段，包括分时数据
- **优点**: 稳定可靠，多种分时数据获取方式
- **特点**: 自动尝试多种方法获取09:35、09:40、09:45的收盘价

### 2. `获取指定字段数据.py` (完整版)
- **功能**: 获取所有字段，包括分时数据
- **优点**: 功能完整
- **缺点**: 需要先用miniQMT下载分时数据

### 3. 数据下载脚本
- `下载日数据.py`: 您的原始日线数据下载脚本
- `下载分钟数据.py`: 已增强，添加了新功能
- `下载分时数据.py`: 专门的分时数据下载和处理工具

## 获取的字段列表

### 基础信息
- 股票代码
- 股票名称  
- 交易日期

### 行情数据
- 开盘价
- 最高价
- 最低价
- 收盘价
- 前收盘价
- 成交量
- 成交额

### 市值数据
- 流通市值
- 总市值

### 财务数据
- 净利润TTM
- 现金流TTM
- 净资产
- 总资产
- 总负债
- 净利润(当季)

### 资金流向数据
- 中户资金买入额
- 中户资金卖出额
- 大户资金买入额
- 大户资金卖出额
- 散户资金买入额
- 散户资金卖出额
- 机构资金买入额
- 机构资金卖出额

### 指数成分股
- 沪深300成分股
- 上证50成分股
- 中证500成分股
- 中证1000成分股
- 中证2000成分股
- 创业板指成分股

### 行业分类
- 新版申万一级行业名称
- 新版申万二级行业名称
- 新版申万三级行业名称

### 分时数据
- 09:35收盘价
- 09:40收盘价
- 09:45收盘价

## 使用步骤

### 1. 环境准备
```bash
pip install tushare pandas openpyxl
# 如果使用完整版，还需要安装
pip install xtquant
```

### 2. 设置Token
在脚本开头修改TOKEN变量：
```python
TOKEN = '你的tushare_token'
```

### 3. 运行脚本
```bash
python 获取股票数据_简化版.py
```

### 4. 选择模式
- 1: 测试单只股票
- 2: 获取前10只股票  
- 3: 获取指定数量股票
- 4: 获取全部股票

## 输出文件
- Excel文件包含两个工作表：
  - `股票数据`: 完整的股票数据
  - `字段说明`: 各字段的详细说明

## 注意事项

1. **频率限制**: 脚本已内置延时，避免触发tushare频率限制
2. **数据完整性**: 部分股票可能缺少某些数据（如财务数据、资金流向等）
3. **分时数据**: 需要先用miniQMT下载对应日期的分钟数据
4. **Token权限**: 确保tushare token有足够权限访问所需数据

## 常见问题

### Q: 为什么某些字段为空？
A: 可能原因：
- 股票当日停牌
- 财务数据尚未公布
- 资金流向数据不可用
- 分时数据未下载

### Q: 如何获取分时数据？
A: 程序会自动尝试多种方法：
1. **xtquant方式**: 如果安装了xtquant，从本地数据获取
2. **tushare方式**: 使用tushare的分钟数据接口（需要权限）
3. **备用方式**: 使用开盘价作为近似值

### Q: 如何提高分时数据获取成功率？
A:
1. 安装xtquant: `pip install xtquant`
2. 运行`下载分时数据.py`下载指定日期的分钟数据
3. 确保tushare账户有足够积分访问分钟数据

### Q: 数据获取很慢怎么办？
A: 
- 减少获取的股票数量
- 检查网络连接
- 确认tushare服务状态

## 技术支持
如有问题，请检查：
1. Token是否正确设置
2. 网络连接是否正常
3. 依赖包是否正确安装
4. 日期格式是否正确
