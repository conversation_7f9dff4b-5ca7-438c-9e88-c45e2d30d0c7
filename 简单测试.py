print("开始测试...")

try:
    import tushare as ts
    print("tushare导入成功")
except Exception as e:
    print(f"tushare导入失败: {e}")
    exit()

try:
    import pandas as pd
    print("pandas导入成功")
except Exception as e:
    print(f"pandas导入失败: {e}")
    exit()

# 测试tushare连接
TOKEN = '2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211'
print(f"使用TOKEN: {TOKEN[:10]}...")

try:
    pro = ts.pro_api(TOKEN)
    print("tushare pro初始化成功")
except Exception as e:
    print(f"tushare pro初始化失败: {e}")
    exit()

# 测试获取股票基础信息
try:
    print("测试获取股票基础信息...")
    stock_info = pro.stock_basic(ts_code='000001.SZ')
    if not stock_info.empty:
        print(f"成功获取股票信息: {stock_info.iloc[0]['name']}")
    else:
        print("未获取到股票信息")
except Exception as e:
    print(f"获取股票基础信息失败: {e}")

# 测试获取日线数据
try:
    print("测试获取日线数据...")
    daily_data = pro.daily(ts_code='000001.SZ', trade_date='20241108')
    if not daily_data.empty:
        print(f"成功获取日线数据: 收盘价 {daily_data.iloc[0]['close']}")
    else:
        print("未获取到日线数据")
except Exception as e:
    print(f"获取日线数据失败: {e}")

print("测试完成")
