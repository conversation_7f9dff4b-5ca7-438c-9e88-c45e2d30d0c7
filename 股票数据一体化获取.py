import datetime
from xtquant import xtdata
import tushare as ts
import pandas as pd
import os
import time
from joblib import Parallel, delayed
import warnings
warnings.filterwarnings("ignore")

# tushare配置
token = '2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211'
pro = ts.pro_api(token)

# miniQMT配置
dividend_type = 'front'  # 复权类型
period = '5m'  # 数据周期

def download_data(stock, period):
    """下载单只股票数据"""
    try:
        xtdata.download_history_data(stock_code=stock, period=period)
    except:
        print(stock, 'data failed')

def get_index_member_info(ts_code):
    """获取指数成分股信息"""
    index_codes = {
        '000300.SH': '沪深300',
        '000016.SH': '上证50',
        '000905.SH': '中证500',
        '000852.SH': '中证1000',
        '932000.CSI': '中证2000',
        '399006.SZ': '创业板指'
    }
    
    member_info = {}
    for index_code, index_name in index_codes.items():
        try:
            members = pro.index_weight(
                index_code=index_code,
                trade_date='',
                fields='index_code,con_code'
            )
            if not members.empty and ts_code in members['con_code'].values:
                member_info[index_name] = 1
            else:
                member_info[index_name] = 0
            time.sleep(0.1)
        except Exception as e:
            print(f"获取{index_name}成分股信息失败: {e}")
            member_info[index_name] = 0
    
    return member_info

def get_industry_info(ts_code):
    """获取申万行业分类信息"""
    try:
        industry_data = pro.index_member_all(
            ts_code=ts_code,
            is_new='Y'
        )
        
        industry_info = {
            '申万一级行业': '',
            '申万二级行业': '',
            '申万三级行业': ''
        }
        
        if not industry_data.empty:
            row = industry_data.iloc[0]
            industry_info = {
                '申万一级行业': row.get('l1_name', ''),
                '申万二级行业': row.get('l2_name', ''),
                '申万三级行业': row.get('l3_name', '')
            }
        
        return industry_info
    except Exception as e:
        print(f"获取申万行业信息失败: {e}")
        return {'申万一级行业': '', '申万二级行业': '', '申万三级行业': ''}

def get_intraday_prices(ts_code, trade_date):
    """从本地5分钟数据获取分时价格"""
    try:
        # 转换股票代码格式
        if ts_code.endswith('.SH'):
            xt_code = ts_code.replace('.SH', '.SS')
        elif ts_code.endswith('.SZ'):
            xt_code = ts_code.replace('.SZ', '.SZ')
        else:
            xt_code = ts_code
        
        # 获取本地5分钟数据
        data = xtdata.get_local_data(
            field_list=['time', 'open','close','high','low','volume','amount'],
            stock_list=[xt_code],
            period='5m',
            dividend_type='front'
        )
        
        if xt_code not in data or data[xt_code].empty:
            return None, None, None
        
        df = data[xt_code]
        df['datetime'] = df['time'].apply(lambda x: datetime.datetime.fromtimestamp(x/1000.0))
        
        # 筛选当日数据
        trade_date_obj = datetime.datetime.strptime(trade_date, '%Y%m%d').date()
        df = df[df['datetime'].dt.date == trade_date_obj]
        
        if df.empty:
            return None, None, None
        
        # 直接按时间筛选
        df['time_str'] = df['datetime'].dt.strftime('%H:%M')
        
        price_0935 = df[df['time_str'] == '09:35']['close'].iloc[0] if len(df[df['time_str'] == '09:35']) > 0 else None
        price_0940 = df[df['time_str'] == '09:40']['close'].iloc[0] if len(df[df['time_str'] == '09:40']) > 0 else None
        price_0945 = df[df['time_str'] == '09:45']['close'].iloc[0] if len(df[df['time_str'] == '09:45']) > 0 else None
        
        return price_0935, price_0940, price_0945
    
    except Exception as e:
        print(f"   获取{ts_code}分时数据失败: {e}")
        return None, None, None

def get_comprehensive_stock_data(ts_code, trade_date):
    """获取单只股票的综合数据"""
    print(f"正在获取股票 {ts_code} 的综合数据...")
    
    result = {
        '股票代码': ts_code,
        '股票名称': '',
        '交易日期': trade_date,
        '开盘价': None, '最高价': None, '最低价': None, '收盘价': None, '前收盘价': None,
        '成交量': None, '成交额': None, '流通市值': None, '总市值': None,
        '净利润TTM': None, '现金流TTM': None, '净资产': None, '总资产': None, '总负债': None, '净利润(当季)': None,
        '中户资金买入额': None, '中户资金卖出额': None, '大户资金买入额': None, '大户资金卖出额': None,
        '散户资金买入额': None, '散户资金卖出额': None, '机构资金买入额': None, '机构资金卖出额': None,
        '沪深300成分股': 0, '上证50成分股': 0, '中证500成分股': 0, '中证1000成分股': 0, '中证2000成分股': 0, '创业板指成分股': 0,
        '新版申万一级行业名称': '', '新版申万二级行业名称': '', '新版申万三级行业名称': '',
        '09:35收盘价': None, '09:40收盘价': None, '09:45收盘价': None
    }
    
    try:
        # 1. 获取基础信息
        stock_info = pro.stock_basic(ts_code=ts_code)
        if not stock_info.empty:
            result['股票名称'] = stock_info.iloc[0]['name']
        
        # 2. 获取日线行情数据
        price_data = pro.daily(
            ts_code=ts_code,
            trade_date=trade_date,
            fields='ts_code,trade_date,open,high,low,close,pre_close,vol,amount'
        )
        
        if not price_data.empty:
            row = price_data.iloc[0]
            result.update({
                '开盘价': row['open'], '最高价': row['high'], '最低价': row['low'],
                '收盘价': row['close'], '前收盘价': row['pre_close'],
                '成交量': row['vol'], '成交额': row['amount']
            })
        
        # 3. 获取市值数据
        daily_basic = pro.daily_basic(
            ts_code=ts_code,
            trade_date=trade_date,
            fields='ts_code,trade_date,circ_mv,total_mv'
        )
        
        if not daily_basic.empty:
            row = daily_basic.iloc[0]
            result.update({'流通市值': row['circ_mv'], '总市值': row['total_mv']})
        
        # 4. 获取财务数据
        end_date = trade_date
        start_date = (datetime.datetime.strptime(trade_date, '%Y%m%d') - datetime.timedelta(days=365*2)).strftime('%Y%m%d')
        
        try:
            balancesheet = pro.balancesheet(
                ts_code=ts_code, start_date=start_date, end_date=end_date,
                fields='ts_code,end_date,total_assets,total_liab,total_hldr_eqy_exc_min_int'
            )
            if not balancesheet.empty:
                latest = balancesheet.iloc[0]
                result.update({
                    '净资产': latest['total_hldr_eqy_exc_min_int'],
                    '总资产': latest['total_assets'],
                    '总负债': latest['total_liab']
                })
        except: pass
        
        try:
            income = pro.income(
                ts_code=ts_code, start_date=start_date, end_date=end_date,
                fields='ts_code,end_date,n_income_attr_p'
            )
            if not income.empty:
                recent_income = income.head(4)
                ttm_profit = recent_income['n_income_attr_p'].sum()
                result['净利润TTM'] = ttm_profit
                result['净利润(当季)'] = income.iloc[0]['n_income_attr_p']
        except: pass
        
        try:
            cashflow = pro.cashflow(
                ts_code=ts_code, start_date=start_date, end_date=end_date,
                fields='ts_code,end_date,n_cashflow_act'
            )
            if not cashflow.empty:
                recent_cashflow = cashflow.head(4)
                ttm_cashflow = recent_cashflow['n_cashflow_act'].sum()
                result['现金流TTM'] = ttm_cashflow
        except: pass
        
        # 5. 获取资金流向数据
        try:
            moneyflow = pro.moneyflow(
                ts_code=ts_code, trade_date=trade_date,
                fields='ts_code,trade_date,buy_sm_amount,sell_sm_amount,buy_md_amount,sell_md_amount,buy_lg_amount,sell_lg_amount,buy_elg_amount,sell_elg_amount'
            )
            if not moneyflow.empty:
                row = moneyflow.iloc[0]
                result.update({
                    '散户资金买入额': row['buy_sm_amount'], '散户资金卖出额': row['sell_sm_amount'],
                    '中户资金买入额': row['buy_md_amount'], '中户资金卖出额': row['sell_md_amount'],
                    '大户资金买入额': row['buy_lg_amount'], '大户资金卖出额': row['sell_lg_amount'],
                    '机构资金买入额': row['buy_elg_amount'], '机构资金卖出额': row['sell_elg_amount']
                })
        except: pass
        
        # 6. 获取指数成分股信息
        try:
            index_member = get_index_member_info(ts_code)
            result.update({
                '沪深300成分股': index_member.get('沪深300', 0),
                '上证50成分股': index_member.get('上证50', 0),
                '中证500成分股': index_member.get('中证500', 0),
                '中证1000成分股': index_member.get('中证1000', 0),
                '中证2000成分股': index_member.get('中证2000', 0),
                '创业板指成分股': index_member.get('创业板指', 0)
            })
        except: pass
        
        # 7. 获取行业信息
        try:
            industry_info = get_industry_info(ts_code)
            result.update({
                '新版申万一级行业名称': industry_info.get('申万一级行业', ''),
                '新版申万二级行业名称': industry_info.get('申万二级行业', ''),
                '新版申万三级行业名称': industry_info.get('申万三级行业', '')
            })
        except: pass
        
        # 8. 获取分时数据
        try:
            price_0935, price_0940, price_0945 = get_intraday_prices(ts_code, trade_date)
            result.update({
                '09:35收盘价': price_0935,
                '09:40收盘价': price_0940,
                '09:45收盘价': price_0945
            })
        except: pass
        
        time.sleep(0.2)  # 避免频率限制
        
    except Exception as e:
        print(f"获取股票 {ts_code} 数据时出错: {e}")
    
    return result

def save_stock_data(df, filename=None):
    """保存股票数据到Excel"""
    if filename is None:
        trade_date = df['交易日期'].iloc[0] if '交易日期' in df.columns else datetime.datetime.now().strftime('%Y%m%d')
        filename = f'股票综合数据_{trade_date}.xlsx'

    try:
        required_columns = [
            '股票代码', '股票名称', '交易日期', '开盘价', '最高价', '最低价', '收盘价', '前收盘价',
            '成交量', '成交额', '流通市值', '总市值', '净利润TTM', '现金流TTM', '净资产', '总资产',
            '总负债', '净利润(当季)', '中户资金买入额', '中户资金卖出额', '大户资金买入额', '大户资金卖出额',
            '散户资金买入额', '散户资金卖出额', '机构资金买入额', '机构资金卖出额', '沪深300成分股',
            '上证50成分股', '中证500成分股', '中证1000成分股', '中证2000成分股', '创业板指成分股',
            '新版申万一级行业名称', '新版申万二级行业名称', '新版申万三级行业名称',
            '09:35收盘价', '09:40收盘价', '09:45收盘价'
        ]

        df_ordered = df.reindex(columns=required_columns)

        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            df_ordered.to_excel(writer, sheet_name='股票数据', index=False)

        print(f"数据已保存到: {filename}")
        return filename

    except Exception as e:
        print(f"保存文件失败: {e}")
        return None

def main():
    """主函数 - 一体化获取股票数据"""
    print("=" * 60)
    print("股票数据一体化获取工具")
    print("=" * 60)
    print("功能: 下载数据 + 获取综合信息 + 分时价格")
    print("=" * 60)

    # 获取交易日期
    date_input = input("请输入交易日期 (格式: YYYY-MM-DD，回车使用今天): ").strip()
    if not date_input:
        trade_date = datetime.datetime.now().strftime('%Y%m%d')
        print(f"使用今天: {datetime.datetime.now().strftime('%Y-%m-%d')}")
    else:
        try:
            trade_date = datetime.datetime.strptime(date_input, '%Y-%m-%d').strftime('%Y%m%d')
        except:
            print("日期格式错误，使用今天")
            trade_date = datetime.datetime.now().strftime('%Y%m%d')

    # 1. 获取股票列表
    print('\n1. 获取股票列表...')
    df = pro.daily_basic(ts_code='', trade_date=trade_date)
    stock_list = df['ts_code'].tolist()
    print(f'获取股票列表完成，共{len(stock_list)}只股票')

    # 选择处理模式
    print("\n选择处理模式:")
    print("1. 只下载数据")
    print("2. 只获取综合数据（需要先下载）")
    print("3. 下载数据 + 获取综合数据")
    print("4. 测试单只股票")

    choice = input("请选择 (1/2/3/4): ").strip()

    if choice == '4':
        # 测试单只股票
        stock_code = input("请输入股票代码 (默认: 000001.SZ): ").strip()
        if not stock_code:
            stock_code = '000001.SZ'

        print(f"\n开始处理 {stock_code}...")

        # 下载数据
        print("下载5分钟数据...")
        download_data(stock_code, period)

        # 获取综合数据
        result = get_comprehensive_stock_data(stock_code, trade_date)

        print(f"\n=== {result['股票名称']} ({result['股票代码']}) ===")
        print(f"收盘价: {result['收盘价']}")
        print(f"总市值: {result['总市值']} 万元")
        print(f"申万一级行业: {result['新版申万一级行业名称']}")
        print(f"09:35收盘价: {result['09:35收盘价']}")
        print(f"09:40收盘价: {result['09:40收盘价']}")
        print(f"09:45收盘价: {result['09:45收盘价']}")

        # 保存结果
        df_result = pd.DataFrame([result])
        save_stock_data(df_result)

    else:
        # 批量处理
        limit_input = input("是否限制处理数量? (输入数字限制，回车处理全部): ").strip()
        if limit_input.isdigit():
            stock_list = stock_list[:int(limit_input)]
            print(f"限制处理前{limit_input}只股票")

        if choice in ['1', '3']:
            # 下载数据
            print(f'\n2. 下载{period}数据...')
            res = Parallel(n_jobs=4)(delayed(download_data)(stock, period) for stock in stock_list)
            print('数据下载完成')

        if choice in ['2', '3']:
            # 获取综合数据
            print(f'\n3. 获取综合数据...')
            all_data = []
            total = len(stock_list)

            for i, ts_code in enumerate(stock_list):
                if i % 50 == 0:
                    print(f'进度: {i}/{total} - {ts_code}')

                try:
                    stock_data = get_comprehensive_stock_data(ts_code, trade_date)
                    all_data.append(stock_data)
                except Exception as e:
                    print(f"处理{ts_code}失败: {e}")
                    continue

            if all_data:
                df_result = pd.DataFrame(all_data)
                print(f'\n成功获取 {len(df_result)} 只股票数据')

                # 显示统计
                print(f"有收盘价数据: {df_result['收盘价'].notna().sum()} 只")
                print(f"有分时数据: {df_result['09:35收盘价'].notna().sum()} 只")

                # 保存数据
                save_stock_data(df_result)
            else:
                print("未获取到任何数据")

if __name__ == "__main__":
    main()
